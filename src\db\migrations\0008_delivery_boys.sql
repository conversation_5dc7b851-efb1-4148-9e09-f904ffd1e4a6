-- Delivery Boys Management System

-- Add role field to users table
ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'customer';

-- Create delivery_boy_locations table to map delivery boys to locations
CREATE TABLE IF NOT EXISTS delivery_boy_locations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  location_id INTEGER NOT NULL,
  is_active BOOLEAN DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIGN KEY (location_id) REFERENCES order_locations (id) ON DELETE CASCADE
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_delivery_boy_locations_user_id ON delivery_boy_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_delivery_boy_locations_location_id ON delivery_boy_locations(location_id);

-- Add delivery_boy_id field to orders table
ALTER TABLE orders ADD COLUMN delivery_boy_id INTEGER;
ALTER TABLE orders ADD COLUMN out_for_delivery_at TEXT;

-- Update order_status options to include 'out_for_delivery'
-- Note: SQLite doesn't support ENUM types, so this is just a comment for reference
-- Valid order_status values: 'placed', 'processing', 'out_for_delivery', 'delivered', 'cancelled'
