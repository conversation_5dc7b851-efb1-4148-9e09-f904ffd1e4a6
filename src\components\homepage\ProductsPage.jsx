import React, { useState, useEffect, useRef, useCallback } from "react";
const ProductsPage = ({
  initialData = {
    products: [],
    categories: [],
    hasMore: false,
    totalCount: 0,
    currentUrl: "",
    canonicalUrl: "",
    baseUrl: "",
    pathMode: true,
    activeFilters: {
      category: "all",
      tags: [],
      minPrice: 0,
      maxPrice: 10000,
    },
    selectedSort: "newest",
    searchQuery: "",
    currentPage: 1,
  },
  children,
}) => {
  // State variables - initialized from server data
  const [products, setProducts] = useState(initialData.products || []);
  const [categories, setCategories] = useState(initialData.categories || []);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMoreProducts, setHasMoreProducts] = useState(initialData.hasMore);
  const [currentPage, setCurrentPage] = useState(initialData.currentPage || 1);
  const [searchQuery, setSearchQuery] = useState(initialData.searchQuery || "");
  const [showNoResults, setShowNoResults] = useState(
    initialData.products.length === 0
  );
  const [showSortModal, setShowSortModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [totalResults, setTotalResults] = useState(initialData.totalCount || 0);
  const [totalCount, setResultsCount] = useState(
    initialData.products.length || 0
  );
  const [totalPages, setTotalPages] = useState(
    Math.ceil((initialData.totalCount || 0) / 12) || 1
  );
  const [productIconStates, setProductIconStates] = useState({});
  // Add transition states for filter changes
  const [isFilterChanging, setIsFilterChanging] = useState(false);
  const [transitionComplete, setTransitionComplete] = useState(true);
  // Flag to use path-based routing
  const pathMode = initialData.pathMode || false;
  // Store the base URL from the server
  const baseUrl = initialData.baseUrl || "";
  // Store canonical URL if available
  const canonicalUrl = initialData.canonicalUrl || "";

  // Filter and sorting states - initialized from server data
  const [selectedSort, setSelectedSort] = useState(
    initialData.selectedSort || "newest"
  );
  const [activeFilters, setActiveFilters] = useState(
    initialData.activeFilters || {
      category: "all",
      tags: [],
      minPrice: 0,
      maxPrice: 10000,
    }
  );

  // Refs for components
  const searchInputRef = useRef(null);
  const formRef = useRef(null);

  // Add a debounce reference to prevent multiple calls
  const searchTimerRef = useRef(null);
  const isInitialMount = useRef(true);

  // Add ref for products container to control animations
  const productsContainerRef = useRef(null);

  // Sort options with enhanced design and descriptions
  const sortOptions = [
    {
      id: "price-asc",
      name: "Price: Low to High",
      icon: "trending_up",
      description: "Products sorted from lowest to highest price",
    },
    {
      id: "price-desc",
      name: "Price: High to Low",
      icon: "trending_down",
      description: "Products sorted from highest to lowest price",
    },
    {
      id: "newest",
      name: "Newest First",
      icon: "new_releases",
      description: "Recently added products shown first",
    },
    {
      id: "bestseller",
      name: "Bestsellers",
      icon: "star",
      description: "Our most popular products",
    },
  ];

  // Helper function to construct path-based URLs
  const constructPathUrl = useCallback(
    (params = {}) => {
      // Create a URL object with origin
      const url = new URL(baseUrl);
      const pathSegments = []; // Start with shop as the base path

      // Add segments based on provided filters in SEO-friendly format

      // 1. Category (most important)
      if (params.category && params.category !== "all") {
        pathSegments.push("category", params.category);
      }

      // 2. Search term (if searching)
      if (params.search) {
        pathSegments.push("search", params.search);
      }

      // 3. Tags/filters
      if (params.tags && params.tags.length > 0) {
        pathSegments.push("filter", params.tags.join(","));
      }

      // 4. Price range
      if (
        (params.minPrice && params.minPrice > 0) ||
        (params.maxPrice && params.maxPrice < 10000)
      ) {
        const minVal = params.minPrice || 0;
        const maxVal = params.maxPrice || 10000;

        if (minVal > 0 && maxVal < 10000) {
          pathSegments.push("price-range", `${minVal}-${maxVal}`);
        } else if (minVal > 0) {
          pathSegments.push("price-range", `above-${minVal}`);
        } else if (maxVal < 10000) {
          pathSegments.push("price-range", `under-${maxVal}`);
        }
      }

      // 5. Sort order (less important for the URL structure)
      if (params.sort && params.sort !== "newest") {
        pathSegments.push("order-by", params.sort);
      }

      // 6. Pagination (least important, at the end)
      if (params.page && params.page > 1) {
        pathSegments.push("page", params.page.toString());
      }

      // Construct the path
      url.pathname = `/${pathSegments.join("/")}`;

      return url.toString();
    },
    [baseUrl]
  );

  // Helper function to construct query parameter URLs (fallback for backward compatibility)
  const constructQueryUrl = useCallback(
    (params = {}) => {
      // Use URL from initialData as a base
      const url = new URL(baseUrl);

      // Add category if not default
      if (params.category && params.category !== "all") {
        url.searchParams.set("category", params.category);
      }

      // Add sort if not default
      if (params.sort && params.sort !== "newest") {
        url.searchParams.set("sort", params.sort);
      }

      // Add search if provided
      if (params.search) {
        url.searchParams.set("search", params.search);
      }

      // Add price range if not default
      if (params.maxPrice && params.maxPrice < 10000) {
        url.searchParams.set("maxPrice", params.maxPrice.toString());
      }

      if (params.minPrice && params.minPrice > 0) {
        url.searchParams.set("minPrice", params.minPrice.toString());
      }

      // Add tags if any
      if (params.tags && params.tags.length > 0) {
        url.searchParams.set("tags", params.tags.join(","));
      }

      // Add page if not the first page
      if (params.page && params.page > 1) {
        url.searchParams.set("page", params.page.toString());
      }

      return url.toString();
    },
    [baseUrl]
  );

  // Choose URL construction method based on path mode
  const constructUrl = useCallback(
    (params = {}) => {
      return pathMode ? constructPathUrl(params) : constructQueryUrl(params);
    },
    [pathMode, constructPathUrl, constructQueryUrl]
  );

  // Focus on search input when the component mounts
  useEffect(() => {
    if (searchQuery && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [searchQuery]);

  // Handle transition effects when filters or sort changes
  useEffect(() => {
    if (isFilterChanging) {
      const timer = setTimeout(() => {
        setTransitionComplete(false);
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [isFilterChanging]);

  // Calculate total pages whenever total results changes
  useEffect(() => {
    // Assuming products per page is 12
    setTotalPages(Math.ceil(totalResults / 12) || 1);
  }, [totalResults]);
  useEffect(() => {
    // Update total count whenever products change
    setResultsCount(initialData.products.length);
  }, [totalCount]);

  // Handle pagination navigation
  const handlePageChange = useCallback(
    (pageNumber) => {
      if (isLoading || pageNumber === currentPage) return;

      setIsLoading(true);

      // Construct URL for fetching the selected page
      const url = constructUrl({
        page: pageNumber,
        category: activeFilters.category,
        sort: selectedSort,
        search: searchQuery,
        minPrice: activeFilters.minPrice,
        maxPrice: activeFilters.maxPrice,
        tags: activeFilters.tags,
      });

      // Navigate with transition
      navigateWithTransition(url);
    },
    [
      activeFilters,
      constructUrl,
      currentPage,
      isLoading,
      searchQuery,
      selectedSort,
    ]
  );

  // Handle filter changes with animation transitions
  const applyFiltersAndSort = () => {
    // Start transition animation before navigation
    setIsFilterChanging(true);
    setTransitionComplete(false);

    // Add a product-container-exit class to the container
    if (productsContainerRef.current) {
      productsContainerRef.current.className =
        productsContainerRef.current.className + " product-container-exit";
    }

    // Construct the URL with the current filters
    const url = constructUrl({
      category: activeFilters.category,
      sort: selectedSort,
      search: searchQuery,
      minPrice: activeFilters.minPrice,
      maxPrice: activeFilters.maxPrice,
      tags: activeFilters.tags,
    });

    // Navigate with transition
    navigateWithTransition(url);
  };

  // Helper function to navigate with animation
  const navigateWithTransition = (url) => {
    // Start transition animation
    setIsFilterChanging(true);
    setTransitionComplete(false);

    // Add exit animation to products container
    if (productsContainerRef.current) {
      productsContainerRef.current.className =
        productsContainerRef.current.className + " product-container-exit";
    }

    // Use setTimeout to allow exit animations to play before navigation
    setTimeout(() => {
      window.location.href = url;
    }, 200); // Small delay to allow exit animations to start properly
  };

  // Handle search input with debounce
  const handleSearch = useCallback(
    (e) => {
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }

      const value = e.target.value;
      setSearchQuery(value);

      searchTimerRef.current = setTimeout(() => {
        // Construct URL for search
        const url = constructUrl({
          search: value,
          category: activeFilters.category,
          sort: selectedSort,
          minPrice: activeFilters.minPrice,
          maxPrice: activeFilters.maxPrice,
          tags: activeFilters.tags,
        });

        // Navigate with transition
        navigateWithTransition(url);
      }, 500); // 500ms debounce time
    },
    [activeFilters, constructUrl, selectedSort]
  );

  // Show notification in the UI
  const showNotification = (message, isError = false) => {
    // Only run this on the client side
    if (typeof document === "undefined") return;

    let notificationContainer = document.querySelector(
      ".notification-container"
    );

    if (!notificationContainer) {
      notificationContainer = document.createElement("div");
      notificationContainer.className =
        "notification-container fixed bottom-20 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-4";
      document.body.appendChild(notificationContainer);
    }

    const toast = document.createElement("div");
    toast.className =
      "bg-white text-gray-800 px-5 py-4 rounded-xl text-sm font-medium shadow-lg opacity-0 transition-all duration-300 transform translate-y-4 border border-gray-100 flex items-center max-w-md";

    let iconName = "info";
    let iconColor = "text-[#5466F7]";

    if (isError) {
      iconName = "error_outline";
      iconColor = "text-red-500";
    } else if (message.includes("added")) {
      iconName = "check_circle";
      iconColor = "text-green-500";
    } else if (message.includes("Applied") || message.includes("applied")) {
      iconName = "local_offer";
    } else if (message.includes("removed")) {
      iconName = "remove_circle_outline";
      iconColor = "text-orange-500";
    } else if (message.includes("reset") || message.includes("filters")) {
      iconName = "filter_list";
    } else if (message.includes("sorted")) {
      iconName = "sort";
    } else if (message.includes("Found")) {
      iconName = "search";
    } else if (message.includes("Showing")) {
      iconName = "category";
    }

    toast.innerHTML = `
      <span class="material-icons-round ${iconColor} mr-2">${iconName}</span>
      <span>${message}</span>
    `;

    notificationContainer.appendChild(toast);

    setTimeout(() => {
      toast.classList.add("opacity-100");
      toast.classList.remove("translate-y-4");
    }, 10);

    setTimeout(() => {
      toast.classList.remove("opacity-100");
      toast.classList.add("opacity-0", "translate-y-4");
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  };

  // Handle haptic feedback if available
  const triggerHapticFeedback = () => {
    if (typeof navigator !== "undefined" && "vibrate" in navigator) {
      navigator.vibrate(40);
    }
  };

  // Apply category filter with navigation and animation
  const handleCategorySelect = (categoryId) => {
    if (categoryId === activeFilters.category) return;

    triggerHapticFeedback();

    const url = constructUrl({
      category: categoryId,
      sort: selectedSort,
      search: searchQuery,
      minPrice: activeFilters.minPrice,
      maxPrice: activeFilters.maxPrice,
      tags: activeFilters.tags,
    });

    // Navigate with transition
    navigateWithTransition(url);
  };

  // Apply sorting with navigation and animation
  const applySort = () => {
    triggerHapticFeedback();
    setShowSortModal(false);

    const url = constructUrl({
      category: activeFilters.category,
      sort: selectedSort,
      search: searchQuery,
      minPrice: activeFilters.minPrice,
      maxPrice: activeFilters.maxPrice,
      tags: activeFilters.tags,
    });

    // Navigate with transition
    navigateWithTransition(url);

    const sortDisplayName =
      sortOptions.find((option) => option.id === selectedSort)?.name ||
      "selected criteria";
    showNotification(`Products sorted by ${sortDisplayName}`);
  };

  // Apply filters with navigation and animation
  const applyFilters = () => {
    triggerHapticFeedback();
    setShowFilterModal(false);

    // Apply filters by navigating with delay for animation
    setTimeout(() => {
      applyFiltersAndSort();
    }, 100);

    let filterMessage = "Filters applied";
    if (activeFilters.tags.length > 0) {
      const tagNames = activeFilters.tags
        .map((tag) =>
          tag === "new_arrivals"
            ? "New Arrivals"
            : tag === "on_sale"
            ? "On Sale"
            : tag
        )
        .join(", ");

      filterMessage = `Showing ${tagNames}`;
      if (activeFilters.maxPrice < 10000) {
        filterMessage += ` under ₹${activeFilters.maxPrice}`;
      }
    } else if (activeFilters.maxPrice < 10000) {
      filterMessage = `Showing products under ₹${activeFilters.maxPrice}`;
    }

    showNotification(filterMessage);
  };

  // Reset filters with navigation
  const resetFilters = () => {
    // Navigate to base URL with transition
    navigateWithTransition(baseUrl);

    showNotification("Filters reset");
  };

  // Create product card with enhanced design
  const ProductCard = ({ product }) => {
    // Get the icon state for this specific product or default to "add"
    const cartIconForProduct = productIconStates[product.id] || "add";

    // Format price properly with error handling
    let priceDisplay = "₹0.00";
    let oldPriceDisplay = "";
    let discountPercentage = null;

    try {
      const price = parseFloat(product.price || 0);
      priceDisplay = `₹${price.toFixed(2)}`;

      if (product.old_price) {
        const oldPrice = parseFloat(product.old_price);
        oldPriceDisplay = `₹${oldPrice.toFixed(2)}`;

        // Calculate discount percentage
        if (oldPrice > price) {
          discountPercentage = Math.round(
            ((oldPrice - price) / oldPrice) * 100
          );
        }
      }
    } catch (e) {
      console.error("Price formatting error:", e);
    }

    // Use base URL constructor for image fallback
    const imageUrl = product.image_url
      ? `${product.image_url}`
      : `${baseUrl}/placeholder-image.svg`;

    return (
      <div
        className="product-card bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg border border-gray-100 transition-all duration-300"
        data-id={product.id}
      >
        <a
          href={`${baseUrl}/product/${product.url_slug}`}
          className="block relative"
        >
          <div className="product-image-container relative overflow-hidden aspect-square">
            <img
              src={imageUrl}
              alt={product.name}
              loading="lazy"
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
            />

            {discountPercentage && (
              <span className="absolute top-3 right-3 bg-[#FF6B35] text-white text-xs font-semibold px-2.5 py-1 rounded-full shadow-md z-10">
                -{discountPercentage}%
              </span>
            )}

            {product.is_new && !discountPercentage && (
              <span className="absolute top-3 left-3 bg-[#2EC4B6] text-white text-xs font-semibold px-2.5 py-1 rounded-full shadow-md z-10">
                New
              </span>
            )}
          </div>
          <div className="p-4">
            {product.category_name && (
              <div className="text-xs text-gray-500 mb-1.5 font-medium truncate">
                {product.category_name}
              </div>
            )}

            <h3 className="font-medium text-gray-800 text-sm leading-tight line-clamp-2 mb-2 h-10">
              {product.name}
            </h3>

            <div className="flex justify-between items-center mt-3">
              <div className="flex flex-col">
                <div className="flex items-baseline gap-1.5">
                  <p className="font-bold text-[#5466F7]">{priceDisplay}</p>
                  {oldPriceDisplay && (
                    <p className="text-xs text-gray-400 line-through">
                      {oldPriceDisplay}
                    </p>
                  )}
                </div>
              </div>
              {/* <button
                className="quick-add-btn w-8 h-8 rounded-full bg-[#FF6B35] text-white flex items-center justify-center shadow-sm hover:bg-[#e55c28] transition-all"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();

                  // Add haptic feedback if available
                  if ("vibrate" in navigator) {
                    navigator.vibrate(50);
                  }

                  // Add to cart
                  if (window.CartUtils) {
                    window.CartUtils.addToCart({
                      id: product.id,
                      name: product.name,
                      price: parseFloat(product.price) || 0,
                      quantity: 1,
                      image: product.image_url,
                      image_url: product.image_url,
                      slug: product.url_slug,
                      unit_type: product.unit_type || 'quantity',
                      unit_value: product.unit_value || 1
                    });

                    // Show animation
                    const btn = e.currentTarget;
                    btn.classList.add("scale-110", "bg-green-500");
                    btn.innerHTML = '<span class="material-icons-round text-lg">check</span>';

                    setTimeout(() => {
                      btn.classList.remove("scale-110", "bg-green-500");
                      btn.innerHTML = '<span class="material-icons-round text-lg">add</span>';
                    }, 1500);

                    // Show notification
                    showNotification(`${product.name} added to cart!`);
                  }
                }}
                aria-label="Add to cart"
              >
                <span className="material-icons-round text-lg">add</span>
              </button> */}
            </div>
          </div>
        </a>
      </div>
    );
  };

  // Pagination component
  const PaginationControls = () => {
    // Don't render pagination if there's only one page or no products
    if (totalPages <= 1 || totalResults === 0) return null;

    // Calculate which page numbers to show
    const getPageNumbers = () => {
      const pageNumbers = [];

      // Always show first page
      pageNumbers.push(1);

      // Current page and surrounding pages
      for (
        let i = Math.max(2, currentPage - 1);
        i <= Math.min(currentPage + 1, totalPages - 1);
        i++
      ) {
        if (!pageNumbers.includes(i)) {
          pageNumbers.push(i);
        }
      }

      // Always show last page if more than 1 page
      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }

      // Add ellipsis where needed
      const result = [];
      let prev = 0;

      for (const page of pageNumbers) {
        if (prev && page - prev > 1) {
          result.push("...");
        }
        result.push(page);
        prev = page;
      }

      return result;
    };

    const pageNumbers = getPageNumbers();
    return (
      <div className="flex justify-center my-8">
        <div className="flex items-center space-x-1">
          {/* Previous page button */}
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`flex items-center justify-center h-10 w-10 rounded-full ${
              currentPage === 1
                ? "text-gray-400 cursor-not-allowed"
                : "text-gray-700 hover:bg-[#5466F7]/10"
            }`}
            aria-label="Previous page"
          >
            <span className="material-icons-round">chevron_left</span>
          </button>

          {/* Page numbers */}
          {pageNumbers.map((page, index) =>
            page === "..." ? (
              <span key={`ellipsis-${index}`} className="px-2 text-gray-500">
                ...
              </span>
            ) : (
              <button
                key={`page-${page}`}
                onClick={() => handlePageChange(page)}
                className={`flex items-center justify-center h-10 w-10 rounded-full ${
                  currentPage === page
                    ? "bg-[#5466F7] text-white"
                    : "text-gray-700 hover:bg-[#5466F7]/10"
                }`}
                aria-label={`Page ${page}`}
                aria-current={currentPage === page ? "page" : undefined}
              >
                {page}
              </button>
            )
          )}

          {/* Next page button */}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`flex items-center justify-center h-10 w-10 rounded-full ${
              currentPage === totalPages
                ? "text-gray-400 cursor-not-allowed"
                : "text-gray-700 hover:bg-[#5466F7]/10"
            }`}
            aria-label="Next page"
          >
            <span className="material-icons-round">chevron_right</span>
          </button>
        </div>
      </div>
    );
  };

  // Product grid with responsive design and animation
  const ProductGrid = () => (
    <div
      className={`grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8 products-grid ${
        isFilterChanging ? "product-container-exit" : "product-container-enter"
      }`}
      ref={productsContainerRef}
      id="products-container"
    >
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );

  // Enhanced skeleton loader with better visual design
  const SkeletonLoader = () => (
    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-20">
      {Array(8)
        .fill(0)
        .map((_, index) => (
          <div
            key={`skeleton-${index}`}
            className="product-skeleton bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100"
          >
            <div className="w-full aspect-square bg-gray-100 animate-pulse relative">
              {index % 3 === 0 && (
                <div className="absolute top-3 right-3 h-5 w-12 bg-gray-200 rounded-full animate-pulse"></div>
              )}
            </div>
            <div className="p-4">
              <div className="h-2 w-16 bg-gray-200 rounded-full animate-pulse mb-3"></div>
              <div className="h-4 w-full bg-gray-200 rounded-full animate-pulse mb-2"></div>
              <div className="h-4 w-3/4 bg-gray-200 rounded-full animate-pulse mb-3"></div>
              <div className="flex justify-between items-center pt-2">
                <div className="h-5 w-16 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
    </div>
  );

  // No results message with dynamic content
  const NoResultsMessage = () => (
    <div className="flex items-center justify-center flex-col py-12 px-4 text-center">
      <h3 className="text-lg font-semibold text-gray-700 mb-2">
        {searchQuery ? `No results for "${searchQuery}"` : "No products found"}
      </h3>
      <p className="text-gray-500 text-center max-w-xs">
        {searchQuery
          ? "Try different keywords or browse categories instead."
          : "Try adjusting your filters to find what you're looking for."}
      </p>

      {/* Action buttons to help user recover */}
      <div className="flex flex-wrap gap-3 mt-6">
        {searchQuery && (
          <button
            onClick={() => {
              setSearchQuery("");
              navigateWithTransition(baseUrl);
              showNotification("Search cleared");
            }}
            className="px-4 py-2.5 bg-[#5466F7]/10 hover:bg-[#5466F7]/20 text-[#5466F7] rounded-lg font-medium transition-colors flex items-center"
          >
            <span className="material-icons-round text-sm mr-1">close</span>
            Clear Search
          </button>
        )}

        {(activeFilters.category !== "all" ||
          activeFilters.tags.length > 0 ||
          activeFilters.maxPrice < 10000) && (
          <button
            onClick={resetFilters}
            className="px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors flex items-center"
          >
            <span className="material-icons-round text-sm mr-1">
              filter_alt_off
            </span>
            Reset Filters
          </button>
        )}
      </div>
    </div>
  );

  // Results stats bar showing count and applied filters
  const ResultsStatsBar = () => {
    if (!totalCount || isLoading) return null;

    return (
      <div className="flex flex-wrap items-center justify-between bg-gray-50 px-4 py-3 rounded-xl mb-4">
        <div className="text-sm text-gray-600">
          <span className="font-semibold text-gray-800">{totalResults}</span>{" "}
          products found
          {searchQuery && (
            <span>
              {" "}
              for "<span className="font-medium">{searchQuery}</span>"
            </span>
          )}
          {activeFilters.category !== "all" && (
            <span>
              {" "}
              in{" "}
              <span className="font-medium">
                {categories.find((c) => c.id === activeFilters.category)
                  ?.name || activeFilters.category}
              </span>
            </span>
          )}
        </div>

        <div className="flex items-center gap-2 flex-shrink-0">
          <span className="text-sm text-gray-500">
            Showing {totalCount} of {totalResults}
          </span>
        </div>
      </div>
    );
  };

  // Applied filters display
  const ActiveFilterChips = () => {
    const hasActiveFilters =
      activeFilters.tags.length > 0 ||
      activeFilters.maxPrice < 10000 ||
      activeFilters.category !== "all";

    if (!hasActiveFilters) return null;

    return (
      <div className="flex flex-wrap gap-2 mb-4">
        {activeFilters.category !== "all" && (
          <div className="bg-[#5466F7]/10 text-[#5466F7] px-3 py-1.5 rounded-lg text-sm font-medium flex items-center">
            <span>
              {categories.find((c) => c.id === activeFilters.category)?.name ||
                activeFilters.category}
            </span>
            <button
              onClick={() => {
                const newFilters = { ...activeFilters, category: "all" };
                const url = constructUrl({
                  ...newFilters,
                  sort: selectedSort,
                  search: searchQuery,
                });
                navigateWithTransition(url);
              }}
              className="ml-1.5 p-0.5 hover:bg-[#5466F7]/10 rounded-full"
            >
              <span className="material-icons-round text-[14px]">close</span>
            </button>
          </div>
        )}

        {activeFilters.tags.map((tag) => (
          <div
            key={tag}
            className="bg-[#5466F7]/10 text-[#5466F7] px-3 py-1.5 rounded-lg text-sm font-medium flex items-center"
          >
            <span>
              {tag === "new_arrivals"
                ? "New Arrivals"
                : tag === "on_sale"
                ? "On Sale"
                : tag}
            </span>
            <button
              onClick={() => {
                const newTags = activeFilters.tags.filter((t) => t !== tag);
                const url = constructUrl({
                  category: activeFilters.category,
                  tags: newTags,
                  minPrice: activeFilters.minPrice,
                  maxPrice: activeFilters.maxPrice,
                  sort: selectedSort,
                  search: searchQuery,
                });
                navigateWithTransition(url);
              }}
              className="ml-1.5 p-0.5 hover:bg-[#5466F7]/10 rounded-full"
            >
              <span className="material-icons-round text-[14px]">close</span>
            </button>
          </div>
        ))}

        {activeFilters.maxPrice < 10000 && (
          <div className="bg-[#5466F7]/10 text-[#5466F7] px-3 py-1.5 rounded-lg text-sm font-medium flex items-center">
            <span>Under ₹{activeFilters.maxPrice}</span>
            <button
              onClick={() => {
                const url = constructUrl({
                  category: activeFilters.category,
                  tags: activeFilters.tags,
                  minPrice: activeFilters.minPrice,
                  maxPrice: 10000,
                  sort: selectedSort,
                  search: searchQuery,
                });
                navigateWithTransition(url);
              }}
              className="ml-1.5 p-0.5 hover:bg-[#5466F7]/10 rounded-full"
            >
              <span className="material-icons-round text-[14px]">close</span>
            </button>
          </div>
        )}

        {hasActiveFilters && (
          <button
            onClick={resetFilters}
            className="bg-gray-100 text-gray-600 px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors flex items-center"
          >
            <span className="material-icons-round text-[14px] mr-1">
              filter_alt_off
            </span>
            Clear All
          </button>
        )}
      </div>
    );
  };

  return (
    <>
      <div className="relative" id="products-container">
        {/* Product specific header controls with improved design */}
        <div className="bg-white z-20 shadow-sm">
          <div className="flex items-center gap-3 px-4 py-2">
            <form
              ref={formRef}
              className="relative flex-1"
              onSubmit={(e) => {
                e.preventDefault();
                applyFiltersAndSort();
              }}
            >
              <input
                type="text"
                ref={searchInputRef}
                name="search"
                placeholder="Search products..."
                className="w-full pl-10 pr-4 py-3.5 bg-white rounded-xl text-sm border border-gray-200 focus:ring-2 focus:ring-[#5466F7] focus:border-transparent outline-none shadow-sm transition-all"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyUp={handleSearch}
              />
              <span className="material-icons-round absolute left-3.5 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                search
              </span>
              {searchQuery && (
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  onClick={() => {
                    setSearchQuery("");
                    navigateWithTransition(baseUrl);
                  }}
                >
                  <span className="material-icons-round text-sm">close</span>
                </button>
              )}
              <button type="submit" className="hidden">
                Search
              </button>
            </form>

            <button
              className="inline-flex items-center justify-center p-3.5 rounded-xl bg-white border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm relative"
              aria-label="Filter products"
              onClick={() => setShowFilterModal(true)}
            >
              <span className="material-icons-round text-gray-700">tune</span>
              {(activeFilters.tags.length > 0 ||
                activeFilters.maxPrice < 10000) && (
                <span className="absolute top-1 right-1 w-2 h-2 rounded-full bg-[#5466F7]"></span>
              )}
            </button>

            <button
              className="inline-flex items-center justify-center p-3.5 rounded-xl bg-white border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm"
              aria-label="Sort products"
              onClick={() => setShowSortModal(true)}
            >
              <span className="material-icons-round text-gray-700">sort</span>
            </button>
          </div>

          {/* Categories Slider with elegant horizontal scrolling */}
          <div className="px-2 pb-1 pt-1">
            <div className="overflow-x-auto scrollbar-hide mb-2">
              <div className="flex space-x-3 py-1">
                {categories.map((category) => (
                  <div
                    key={category.id}
                    className="category-slide flex-shrink-0 cursor-pointer"
                    data-category={category.id}
                    onClick={() => handleCategorySelect(category.id)}
                  >
                    <div
                      className={`px-5 py-3 rounded-xl border transition-all ${
                        category.id === activeFilters.category
                          ? "border-[#5466F7] bg-[#5466F7] text-white shadow-md"
                          : "border-gray-200 bg-white text-gray-700 hover:border-[#5466F7]/30 hover:bg-[#5466F7]/5"
                      }`}
                    >
                      <p className={`whitespace-nowrap font-medium`}>
                        {category.name}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Display active filter chips */}
          </div>
        </div>

        {/* Products Display Section */}
        <div
          className={`px-4 py-2 ${
            isFilterChanging
              ? "opacity-50 transition-opacity duration-300"
              : "opacity-100 transition-opacity duration-300"
          }`}
        >
          <ActiveFilterChips />
          {/* Results statistics */}
          {!isLoading && <ResultsStatsBar />}

          {/* Content based on state */}
          {isLoading || (isFilterChanging && !transitionComplete) ? (
            <SkeletonLoader />
          ) : showNoResults ? (
            <NoResultsMessage />
          ) : (
            <>
              <ProductGrid />
              <PaginationControls />
            </>
          )}

          {/* Products count summary at bottom */}
          {totalCount > 0 && (
            <div className="text-center text-sm text-gray-500 mb-8">
              Showing {Math.min((currentPage - 1) * 12 + 1, totalResults)} to{" "}
              {Math.min(currentPage * 12, totalResults)} of {totalResults}{" "}
              products
            </div>
          )}
        </div>
      </div>

      {/* Enhanced sort options modal with categories */}
      {showSortModal && (
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm z-50 transition-opacity opacity-100 duration-300">
          <div
            className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-[80vh] overflow-y-auto transform transition-transform duration-300 shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-center pt-3 pb-1">
              <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
            </div>
            <div className="p-5">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-800 flex items-center">
                  <span className="material-icons-round text-[#5466F7] mr-3">
                    sort
                  </span>
                  Sort Products
                </h2>
                <button
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                  onClick={() => setShowSortModal(false)}
                >
                  <span className="material-icons-round text-gray-500">
                    close
                  </span>
                </button>
              </div>
            </div>
            <div>
              {sortOptions.map((option) => (
                <button
                  key={option.id}
                  className={`w-full text-left py-4 px-5 hover:bg-blue-50/40 transition-colors relative ${
                    option.id === selectedSort ? "bg-blue-50" : ""
                  }`}
                  onClick={() => setSelectedSort(option.id)}
                >
                  <div className="flex items-center">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        option.id === selectedSort
                          ? "bg-[#5466F7] text-white"
                          : "bg-gray-100"
                      } mr-4`}
                    >
                      <span className="material-icons-round">
                        {option.icon}
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-800">
                        {option.name}
                      </div>
                      <div className="text-xs text-gray-500 mt-0.5">
                        {option.description}
                      </div>
                    </div>
                    <span
                      className={`material-icons-round text-[#5466F7] ${
                        option.id === selectedSort ? "" : "opacity-0"
                      }`}
                    >
                      check_circle
                    </span>
                  </div>
                </button>
              ))}
            </div>
            <div className="p-5 border-t border-gray-100 mt-2">
              <button
                onClick={applySort}
                className="w-full py-3.5 bg-[#5466F7] text-white rounded-xl font-medium hover:bg-[#4555e2] transition-all shadow-md flex items-center justify-center"
              >
                <span className="material-icons-round mr-2">check</span>
                Apply
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced filter modal with better UX */}
      {showFilterModal && (
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm z-50 transition-opacity opacity-100 duration-300">
          <div
            className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-[90vh] overflow-y-auto transform transition-transform duration-300 shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-center pt-3 pb-1">
              <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
            </div>
            <div className="p-5">
              <div className="flex justify-between items-center mb-5">
                <h2 className="text-xl font-semibold text-gray-800 flex items-center">
                  <span className="material-icons-round text-[#5466F7] mr-3">
                    tune
                  </span>
                  Filter Products
                </h2>
                <button
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                  onClick={() => setShowFilterModal(false)}
                >
                  <span className="material-icons-round text-gray-500">
                    close
                  </span>
                </button>
              </div>

              <div className="py-2 space-y-8">
                {/* Category filters with a cleaner look */}
                <div>
                  <h3 className="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <span className="material-icons-round text-[#5466F7] mr-2 text-base">
                      category
                    </span>
                    Categories
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    {categories.map((category) => (
                      <label
                        key={category.id}
                        className={`flex items-center gap-3 p-3.5 rounded-xl border cursor-pointer transition-colors ${
                          category.id === activeFilters.category
                            ? "border-[#5466F7] bg-blue-50"
                            : "border-gray-200 hover:border-[#5466F7]/30 hover:bg-blue-50/30"
                        }`}
                      >
                        <input
                          type="radio"
                          name="category"
                          checked={category.id === activeFilters.category}
                          onChange={() =>
                            setActiveFilters((prev) => ({
                              ...prev,
                              category: category.id,
                            }))
                          }
                          className="rounded-full text-[#5466F7] focus:ring-[#5466F7] h-4 w-4"
                        />
                        <span className="text-gray-700 font-medium">
                          {category.name}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Range with improved slider */}
                <div>
                  <h3 className="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <span className="material-icons-round text-[#5466F7] mr-2 text-base">
                      payments
                    </span>
                    Price Range
                  </h3>
                  <div className="px-2">
                    <input
                      type="range"
                      min="0"
                      max="10000"
                      step="500"
                      value={activeFilters.maxPrice}
                      onChange={(e) =>
                        setActiveFilters((prev) => ({
                          ...prev,
                          maxPrice: parseInt(e.target.value),
                        }))
                      }
                      className="w-full accent-[#5466F7]"
                      style={{
                        background: `linear-gradient(to right, #5466F7 ${
                          activeFilters.maxPrice / 100
                        }%, #e5e7eb ${activeFilters.maxPrice / 100}%)`,
                      }}
                    />
                    <div className="flex justify-between text-sm text-gray-600 mt-3 font-medium">
                      <span>₹0</span>
                      <span className="text-[#5466F7] font-semibold">
                        ₹{activeFilters.maxPrice.toLocaleString()}
                      </span>
                    </div>

                    {/* Price range presets for quicker selection */}
                    <div className="flex flex-wrap gap-2 mt-4">
                      {[1000, 2500, 5000, 10000].map((price) => (
                        <button
                          key={price}
                          onClick={() =>
                            setActiveFilters((prev) => ({
                              ...prev,
                              maxPrice: price,
                            }))
                          }
                          className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                            activeFilters.maxPrice === price
                              ? "bg-[#5466F7] text-white"
                              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                          }`}
                        >
                          Under ₹{price.toLocaleString()}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Product Tags with visual improvements */}
                <div>
                  <h3 className="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <span className="material-icons-round text-[#5466F7] mr-2 text-base">
                      local_offer
                    </span>
                    Product Tags
                  </h3>
                  <div className="space-y-4">
                    <label
                      className={`flex items-center justify-between p-3.5 rounded-xl border cursor-pointer transition-colors ${
                        activeFilters.tags.includes("new_arrivals")
                          ? "border-[#5466F7]/60 bg-blue-50"
                          : "border-gray-200 hover:border-[#5466F7]/30 hover:bg-blue-50/30"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-[#2EC4B6]/10 flex items-center justify-center">
                          <span className="material-icons-round text-[#2EC4B6]">
                            new_releases
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-800 font-medium block">
                            New Arrivals
                          </span>
                          <span className="text-xs text-gray-500">
                            Products added recently
                          </span>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={activeFilters.tags.includes("new_arrivals")}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setActiveFilters((prev) => ({
                              ...prev,
                              tags: [...prev.tags, "new_arrivals"],
                            }));
                          } else {
                            setActiveFilters((prev) => ({
                              ...prev,
                              tags: prev.tags.filter(
                                (tag) => tag !== "new_arrivals"
                              ),
                            }));
                          }
                        }}
                        className="rounded text-[#5466F7] focus:ring-[#5466F7] h-5 w-5"
                      />
                    </label>

                    <label
                      className={`flex items-center justify-between p-3.5 rounded-xl border cursor-pointer transition-colors ${
                        activeFilters.tags.includes("on_sale")
                          ? "border-[#5466F7]/60 bg-blue-50"
                          : "border-gray-200 hover:border-[#5466F7]/30 hover:bg-blue-50/30"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-[#FF6B35]/10 flex items-center justify-center">
                          <span className="material-icons-round text-[#FF6B35]">
                            sell
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-800 font-medium block">
                            On Sale
                          </span>
                          <span className="text-xs text-gray-500">
                            Discounted products
                          </span>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={activeFilters.tags.includes("on_sale")}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setActiveFilters((prev) => ({
                              ...prev,
                              tags: [...prev.tags, "on_sale"],
                            }));
                          } else {
                            setActiveFilters((prev) => ({
                              ...prev,
                              tags: prev.tags.filter(
                                (tag) => tag !== "on_sale"
                              ),
                            }));
                          }
                        }}
                        className="rounded text-[#5466F7] focus:ring-[#5466F7] h-5 w-5"
                      />
                    </label>

                    <label
                      className={`flex items-center justify-between p-3.5 rounded-xl border cursor-pointer transition-colors ${
                        activeFilters.tags.includes("bestseller")
                          ? "border-[#5466F7]/60 bg-blue-50"
                          : "border-gray-200 hover:border-[#5466F7]/30 hover:bg-blue-50/30"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-[#FFBC42]/10 flex items-center justify-center">
                          <span className="material-icons-round text-[#FFBC42]">
                            star
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-800 font-medium block">
                            Bestsellers
                          </span>
                          <span className="text-xs text-gray-500">
                            Most popular items
                          </span>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={activeFilters.tags.includes("bestseller")}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setActiveFilters((prev) => ({
                              ...prev,
                              tags: [...prev.tags, "bestseller"],
                            }));
                          } else {
                            setActiveFilters((prev) => ({
                              ...prev,
                              tags: prev.tags.filter(
                                (tag) => tag !== "bestseller"
                              ),
                            }));
                          }
                        }}
                        className="rounded text-[#5466F7] focus:ring-[#5466F7] h-5 w-5"
                      />
                    </label>
                  </div>
                </div>

                {/* Filter action buttons */}
                <div className="flex gap-4 mt-8 pt-6 border-t border-gray-100">
                  <button
                    onClick={resetFilters}
                    className="flex-1 py-3.5 border border-gray-200 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-colors flex items-center justify-center"
                  >
                    <span className="material-icons-round mr-2 text-base">
                      restart_alt
                    </span>
                    Reset
                  </button>
                  <button
                    onClick={applyFilters}
                    className="flex-1 py-3.5 bg-[#5466F7] text-white rounded-xl font-medium hover:bg-[#4555e2] transition-colors shadow-md flex items-center justify-center"
                  >
                    <span className="material-icons-round mr-2 text-base">
                      check
                    </span>
                    Apply
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        /* Enhanced range slider styling with dynamic fill */
        input[type="range"] {
          height: 6px;
          border-radius: 5px;
          -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
          -webkit-appearance: none;
          height: 22px;
          width: 22px;
          border-radius: 50%;
          background: #5466f7;
          box-shadow: 0 2px 4px rgba(84, 102, 247, 0.3);
          cursor: pointer;
          border: 3px solid white;
          margin-top: -8px;
        }

        input[type="range"]::-moz-range-thumb {
          height: 22px;
          width: 22px;
          border-radius: 50%;
          background: #5466f7;
          box-shadow: 0 2px 4px rgba(84, 102, 247, 0.3);
          cursor: pointer;
          border: 3px solid white;
        }

        /* Checkbox styling */
        input[type="checkbox"] {
          cursor: pointer;
        }

        /* Product transition animations */
        .product-container-enter {
          animation: fadeIn 0.5s ease forwards;
        }

        .product-container-exit {
          animation: fadeOut 0.3s ease forwards;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes fadeOut {
          from {
            opacity: 1;
            transform: translateY(0);
          }
          to {
            opacity: 0;
            transform: translateY(-10px);
          }
        }
      `}</style>
    </>
  );
};

export default ProductsPage;
