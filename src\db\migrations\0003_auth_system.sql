-- -- Authentication System Tables

-- -- Modify the existing users table to include authentication fields
-- ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT 0;

-- -- Create OTP table for verification codes
-- CREATE TABLE IF NOT EXISTS otp_verifications (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   phone_number TEXT NOT NULL,
--   otp_code TEXT NOT NULL,
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   expires_at TIMESTAMP NOT NULL,
--   is_used BOOLEAN DEFAULT 0,
--   attempts INTEGER DEFAULT 0
-- );

-- -- Add index for faster lookups
-- CREATE INDEX IF NOT EXISTS idx_otp_phone_number ON otp_verifications(phone_number);
ALTER TABLE users ADD COLUMN email TEXT ;