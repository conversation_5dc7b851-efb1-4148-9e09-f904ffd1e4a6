---
import MainLayout from "../layouts/MainLayout.astro";
import DeliveryDashboard from "../components/delivery/DeliveryDashboard";
---

<MainLayout
  title="Delivery Dashboard - HighQ Foods"
  headerTitle="Delivery Dashboard"
  showHeader={true}
>
  <DeliveryDashboard client:load />
</MainLayout>

<script>
  // Check authentication status on page load
  document.addEventListener("astro:page-load", () => {
    // Check if user is authenticated
    if (typeof window.ApiClient !== "undefined") {
      if (!window.ApiClient.isAuthenticated()) {
        // Save current URL to redirect back after login
        const currentPath = window.location.pathname;
        window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
      }
    }
  });
</script>
