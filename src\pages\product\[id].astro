---
import MainLayout from "../../layouts/MainLayout.astro";
import { getProductBySlug, type ProductDetail } from "../../db/database";

// Disable prerendering to use server-side rendering with Cloudflare D1
export const prerender = false;

// Get product slug from URL params
const { id: slug } = Astro.params;

if (!slug) {
  return new Response("Product slug is required", { status: 400 });
}

// Fetch product data from the database
const product = await getProductBySlug(Astro.locals.runtime.env, slug);
// If product not found, redirect to 404 page
if (!product) {
  console.warn(`No product found with slug: ${slug}`);
  return Astro.redirect('/404');
}

// Prepare all images for the slider (main image + additional images)
const allImages = [product.image, ...(product.additionalImages || [])].filter(
  Boolean
);

// Calculate discount percentage if applicable
let discountPercentage: number | null = null;
if (product.old_price && product.price) {
  try {
    // Handle price as string (with currency symbol) or number
    const oldPrice = typeof product.old_price === 'string'
      ? parseFloat(product.old_price.replace(/[^\d.]/g, ""))
      : product.old_price;

    const currentPrice = typeof product.price === 'string'
      ? parseFloat(product.price.replace(/[^\d.]/g, ""))
      : product.price;

    if (oldPrice > currentPrice) {
      discountPercentage = Math.round(
        ((oldPrice - currentPrice) / oldPrice) * 100
      );
    }
  } catch (e) {
    console.error("Error calculating discount:", e);
  }
}

// Format category for display
const categoryName = product.category || null;
const categorySlug = product.category_id

// Check if we have valid nutritional info
const hasNutritionalInfo =
  product.nutritionalInfo &&
  typeof product.nutritionalInfo === "object" &&
  Object.keys(product.nutritionalInfo || {}).some(
    (key) => {
      const info = product.nutritionalInfo as Record<string, string | undefined>;
      return !!info[key];
    }
  );

// Check if product has tags (not in the type definition but might be added)
const productTags = (product as any).tags;
const hasTags =
  productTags && Array.isArray(productTags) && productTags.length > 0;

// Check if we have valid related products
const hasRelatedProducts =
  product.relatedProducts &&
  Array.isArray(product.relatedProducts) &&
  product.relatedProducts.length > 0;

// Prepare SEO metadata
const title = `${product.name} - HighQ Foods`;
const description = product.description
  ? product.description.substring(0, 160)
  : `${product.name} - Order online from HighQ Foods`;
const canonicalURL = new URL(`/product/${slug}`, Astro.url.origin).href;

// Prepare structured data for product
const productSchema = {
  "@context": "https://schema.org/",
  "@type": "Product",
  "name": product.name,
  "image": allImages,
  "description": product.description || "",
  "sku": product.id.toString(),
  "mpn": product.id.toString(),
  "brand": {
    "@type": "Brand",
    "name": "HighQ Foods"
  },
  "offers": {
    "@type": "Offer",
    "url": canonicalURL,
    "priceCurrency": "INR",
    "price": typeof product.price === 'string'
      ? parseFloat(product.price.replace(/[^\d.]/g, ""))
      : product.price,
    "availability": product.is_available
      ? "https://schema.org/InStock"
      : "https://schema.org/OutOfStock",
    "itemCondition": "https://schema.org/NewCondition"
  }
};
---

<MainLayout
  title={title}
  description={description}
  canonicalURL={canonicalURL}
  image={product.image}
  showFooter={true}
  showHeader={true}
  headerTitle="Product Details"
  showBackButton={true}
  schema={productSchema}
>
  <!-- Breadcrumbs for SEO -->
  <nav class="px-5 py-2 text-sm text-gray-500" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-1">
      <li>
        <a href="/" class="hover:text-gray-700">Home</a>
      </li>
      <li class="flex items-center space-x-1">
        <span class="material-icons-round text-gray-400 text-xs"
          >navigate_next</span
        >
        {
          categoryName && (
            <a
              href={`/category/${categorySlug}`}
              class="hover:text-gray-700"
            >
              {categoryName}
            </a>
          )
        }
        {
          !categoryName && (
            <a href="/menu" class="hover:text-gray-700">
              Menu
            </a>
          )
        }
      </li>
      <li class="flex items-center space-x-1">
        <span class="material-icons-round text-gray-400 text-xs"
          >navigate_next</span
        >
        <span class="text-gray-700 font-medium line-clamp-1" aria-current="page"
          >{product.name}</span
        >
      </li>
    </ol>
  </nav>

  <div class="bg-white pb-20">
    <!-- Product Details -->
    <div class="relative">
      <!-- Enhanced Image Slider with modern design -->
      <div
        class="relative h-[90vw] max-h-[500px] touch-manipulation bg-gray-100"
      >
        <!-- Main image slider container with smooth transitions -->
        <div class="overflow-hidden h-full relative">
          <div
            id="image-slider"
            class="flex h-full snap-x snap-mandatory transition-transform duration-300 ease-out"
          >
            {
              allImages.map((img, index) => (
                <div class="min-w-full w-full h-full flex-shrink-0 snap-center snap-always overflow-hidden flex items-center justify-center">
                  <img
                    src={img}
                    alt={`${product.name} - ${index === 0 ? 'Main Product Image' : `Additional Image ${index}`}`}
                    class="w-full h-full object-contain transform transition-transform duration-200"
                    loading={index === 0 ? "eager" : "lazy"}
                    width="500"
                    height="500"
                    fetchpriority={index === 0 ? "high" : "auto"}
                  />
                </div>
              ))
            }
          </div>

          <!-- Gradient overlay for better text visibility -->
          <div
            class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/40 to-transparent h-20 pointer-events-none"
          >
          </div>

          <!-- Modernized image counter badge -->
          {
            allImages.length > 1 && (
              <div class="absolute bottom-4 right-4 bg-black/60 backdrop-blur-sm text-white text-xs px-3 py-1.5 rounded-full font-medium">
                <span id="current-slide">1</span>/
                <span>{allImages.length}</span>
              </div>
            )
          }
        </div>

        <!-- Enhanced slider pagination indicators with better visibility -->
        {
          allImages.length > 1 && (
            <div class="absolute bottom-4 left-0 right-0 flex justify-center gap-2 px-4">
              {allImages.map((_, index) => (
                <button
                  class={`h-1.5 rounded-full transition-all duration-300 ${index === 0 ? "bg-white w-6" : "bg-white/50 w-3"}`}
                  data-index={index}
                  aria-label={`Go to image ${index + 1}`}
                />
              ))}
            </div>
          )
        }

        <!-- Modern navigation arrows with better touch targets -->
        {
          allImages.length > 1 && (
            <>
              <button
                id="prev-slide"
                class="absolute left-3 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center rounded-full bg-white/20 backdrop-blur-sm text-white shadow-lg transition-all opacity-70 hover:opacity-100 hover:bg-white/30"
                aria-label="Previous image"
              >
                <span class="material-icons-round">chevron_left</span>
              </button>
              <button
                id="next-slide"
                class="absolute right-3 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center rounded-full bg-white/20 backdrop-blur-sm text-white shadow-lg transition-all opacity-70 hover:opacity-100 hover:bg-white/30"
                aria-label="Next image"
              >
                <span class="material-icons-round">chevron_right</span>
              </button>
            </>
          )
        }

        <!-- Enhanced product badges with better visibility -->
        <div class="absolute top-4 left-4 flex flex-col gap-2">
          {
            discountPercentage && (
              <span class="bg-red-500 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-md">
                -{discountPercentage}% OFF
              </span>
            )
          }
          {
            product.is_new && !discountPercentage ? (
              <span class="bg-[#2EC4B6] text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-md">
                New Arrival
              </span>
            ) : null
          }
          {
            product.is_on_sale && !discountPercentage ? (
              <span class="bg-[#FFBC42] text-gray-800 text-xs font-semibold px-3 py-1.5 rounded-full shadow-md">
                On Sale
              </span>
            ) : null
          }
        </div>
      </div>

      <!-- Product information card with material design inspired shadow -->
      <div class="relative -mt-6 rounded-t-3xl bg-white shadow-sm z-10">
        <!-- Primary product info section with better spacing -->
        <div class="px-5 pt-6 pb-5 border-b border-gray-100">
          <h1 class="text-xl font-bold text-gray-800 leading-tight mb-2">
            {product.name}
          </h1>
          <!-- add product id -->
          <p class="text-sm text-gray-500 mb-2" id="product-id">
            Product ID: {product.id}
          </p>

          <div class="flex flex-wrap items-center gap-2 mb-3">
            {/* Product category badge if available */}
            {
              categoryName && (
                <div class="text-xs text-gray-600 bg-gray-100 px-2.5 py-1 rounded-md">
                  {categoryName}
                </div>
              )
            }

            {/* Unit information badge */}
            {
              product.unit_type && product.unit_value && (
                <div class="text-xs text-blue-600 bg-blue-50 px-2.5 py-1 rounded-md">
                  {product.unit_type === "quantity"
                    ? `${product.unit_value} ${product.unit_value > 1 ? "items" : "item"}`
                    : `${product.unit_value}${product.unit_type}`}
                </div>
              )
            }

            {
              product.unit_type && product.unit_value && (
                <div
                  class="text-xs text-gray-600 bg-gray-100 px-2.5 py-1 rounded-md hidden"
                  id="unit_type"
                >
                  {product.unit_type}
                </div>
              )
            }
            {
              product.unit_type && product.unit_value && (
                <div
                  class="text-xs text-gray-600 bg-gray-100 px-2.5 py-1 rounded-md hidden"
                  id="unit_value"
                >
                  {product.unit_value}
                </div>
              )
            }
          </div>

          <!-- Enhanced price display with better visual hierarchy -->
          <div class="flex items-baseline gap-3 mt-3">
            <div class="text-2xl font-bold text-gray-900">{product.price}</div>
            {
              product.old_price ? (
                <div class="text-base line-through text-gray-400">
                  {product.old_price}
                </div>
              ) : null
            }
            {
              discountPercentage ? (
                <div class="text-sm font-semibold text-red-500 bg-red-50 px-2 py-0.5 rounded">
                  Save {discountPercentage}%
                </div>
              ) : null
            }
          </div>
          <div class="text-sm text-gray-500 mt-1.5">
            Price includes all taxes
          </div>
        </div>

        <!-- Actions toolbar with modern design -->
        <div
          class="flex justify-between items-center p-5 bg-white border-b border-gray-100"
        >
          <div class="flex items-center gap-4">
            <button
              id="favorite-button"
              class="flex flex-col items-center justify-center gap-1"
            >
              <div
                class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-700"
              >
                <span class="material-icons-round">favorite_border</span>
              </div>
              <span class="text-xs text-gray-600">Save</span>
            </button>
          </div>

          <!-- Quick quantity and add to cart section -->
          <div class="flex items-center gap-2">
            <div
              class="flex items-center border border-gray-300 rounded-full overflow-hidden"
            >
              <button
                id="decrease-qty"
                class="w-8 h-8 flex items-center justify-center text-gray-600 bg-gray-50"
              >
                <span class="material-icons-round text-sm">remove</span>
              </button>
              <input
                type="text"
                id="quantity"
                value="1"
                readonly
                class="w-8 text-center border-0 p-0 text-gray-800 bg-transparent focus:ring-0"
              />
              <button
                id="increase-qty"
                class="w-8 h-8 flex items-center justify-center text-gray-600 bg-gray-50"
              >
                <span class="material-icons-round text-sm">add</span>
              </button>
            </div>

            <button
              id="add-to-cart"
              class="px-4 py-2 bg-[#FF6B35] text-white rounded-full font-medium flex items-center justify-center gap-1.5 transition-colors hover:bg-[#e55c28] shadow-sm"
            >
              <span class="material-icons-round text-sm">shopping_cart</span>
              <span>Add</span>
            </button>
          </div>
        </div>

        <!-- Enhanced description with better typography -->
        {
          product.description && (
            <div class="p-5 border-b border-gray-100">
              <h2 class="font-semibold text-gray-800 mb-3 flex items-center">
                <span class="material-icons-round text-gray-600 mr-2 text-sm">
                  description
                </span>
                About this item
              </h2>
              <p class="text-gray-600 text-sm leading-relaxed">
                {product.description}
              </p>
            </div>
          )
        }

        <!-- Product tags with modern design -->
        {
          hasTags && productTags && (
            <div class="p-5 border-b border-gray-100">
              <h2 class="font-semibold text-gray-800 mb-3 flex items-center">
                <span class="material-icons-round text-gray-600 mr-2 text-sm">
                  local_offer
                </span>
                Product Tags
              </h2>
              <div class="flex flex-wrap gap-2">
                {productTags.map((tag: string) => (
                  <span class="bg-gray-50 text-gray-700 text-xs px-3 py-1.5 rounded-md border border-gray-200">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )
        }

        <!-- Enhanced nutritional information with icons -->
        {
          hasNutritionalInfo && product.nutritionalInfo && (
            <div class="p-5 border-b border-gray-100">
              <h2 class="font-semibold text-gray-800 mb-3 flex items-center">
                <span class="material-icons-round text-gray-600 mr-2 text-sm">
                  restaurant_menu
                </span>
                Nutritional Information
              </h2>
              <div class="space-y-3 text-sm">
                {product.nutritionalInfo.calories && (
                  <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-orange-50 flex items-center justify-center mr-3">
                      <span class="material-icons-round text-orange-500 text-sm">
                        local_fire_department
                      </span>
                    </div>
                    <div class="flex-1">
                      <span class="text-gray-600">Calories</span>
                      <div class="font-medium text-gray-800">
                        {product.nutritionalInfo.calories}
                      </div>
                    </div>
                  </div>
                )}

                {product.nutritionalInfo.allergens && (
                  <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-red-50 flex items-center justify-center mr-3">
                      <span class="material-icons-round text-red-500 text-sm">
                        error_outline
                      </span>
                    </div>
                    <div class="flex-1">
                      <span class="text-gray-600">Allergens</span>
                      <div class="font-medium text-gray-800">
                        {product.nutritionalInfo.allergens}
                      </div>
                    </div>
                  </div>
                )}

                {product.nutritionalInfo.ingredients && (
                  <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3">
                      <span class="material-icons-round text-green-500 text-sm">
                        eco
                      </span>
                    </div>
                    <div class="flex-1">
                      <span class="text-gray-600">Ingredients</span>
                      <div class="font-medium text-gray-800">
                        {product.nutritionalInfo.ingredients}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )
        }

        <!-- Buy it now button - full width prominent CTA -->
        <div class="p-5 border-b border-gray-100">
          <button
            id="buy-now"
            class="w-full py-3.5 bg-[#5466F7] text-white rounded-xl font-medium flex items-center justify-center gap-2 transition-colors hover:bg-[#4555e5] shadow-md"
          >
            <span class="material-icons-round text-base">shopping_bag</span>
            <span>Buy Now</span>
          </button>
        </div>

        <!-- Related products with enhanced grid layout -->
        {
          hasRelatedProducts && product.relatedProducts && (
            <div class="pt-5 pb-2">
              <div class="flex justify-between items-center mb-4 px-5">
                <h2 class="font-semibold text-gray-800 flex items-center">
                  <span class="material-icons-round text-gray-600 mr-2 text-sm">
                    recommend
                  </span>
                  You may also like
                </h2>
                <a
                  href="/"
                  class="text-sm text-[#5466F7] font-medium flex items-center"
                >
                  View all
                  <span class="material-icons-round text-[#5466F7] text-base ml-0.5">
                    chevron_right
                  </span>
                </a>
              </div>

              <div class="px-3">
                <div class="flex overflow-x-auto pb-4 scrollbar-hide snap-x gap-4">
                  {product.relatedProducts.map((relatedProduct) => (
                    <a
                      href={`/product/${relatedProduct.url_slug}`}
                      class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden active:opacity-90 transition flex-shrink-0 w-40 snap-start"
                    >
                      <div class="aspect-square overflow-hidden relative">
                        <img
                          src={relatedProduct.image}
                          alt={relatedProduct.name}
                          loading="lazy"
                          width="160"
                          height="160"
                          class="w-full h-full object-cover"
                        />
                        {relatedProduct.is_on_sale && (
                          <div class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                            Sale
                          </div>
                        )}
                      </div>
                      <div class="p-3">
                        <p class="text-sm font-medium text-gray-800 line-clamp-1">
                          {relatedProduct.name}
                        </p>
                        <div class="flex items-center justify-between mt-1.5">
                          <p class="text-sm font-semibold text-[#5466F7]">
                            {relatedProduct.price}
                          </p>
                        </div>
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            </div>
          )
        }
      </div>
    </div>
  </div>

  <!-- Fullscreen image viewer with modern design -->
  <div id="fullscreen-viewer" class="fixed inset-0 bg-black z-50 hidden">
    <div class="w-full h-full">
      <!-- Close button with better positioning -->
      <button
        id="close-fullscreen"
        class="absolute top-6 right-6 z-10 bg-black/40 backdrop-blur-sm rounded-full w-12 h-12 flex items-center justify-center"
      >
        <span class="material-icons-round text-white">close</span>
      </button>

      <!-- Fullscreen slider with pinch zoom support -->
      <div class="w-full h-full relative overflow-hidden">
        <div
          id="fullscreen-slider"
          class="flex h-full w-full snap-x snap-mandatory touch-pan-x"
        >
          {
            allImages.map((img, index) => (
              <div class="min-w-full w-full h-full flex-shrink-0 snap-center snap-always flex items-center justify-center">
                <div class="pinch-zoom">
                  <img
                    src={img}
                    alt={`${product.name} - Image ${index + 1} (fullscreen)`}
                    class="max-h-full max-w-full object-contain"
                  />
                </div>
              </div>
            ))
          }
        </div>

        <!-- Improved fullscreen counter -->
        <div
          class="absolute bottom-8 left-0 right-0 flex justify-center text-white"
        >
          <div
            class="bg-black/60 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium"
          >
            <span id="fullscreen-current">1</span> / <span
              >{allImages.length}</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</MainLayout>



<script is:inline>
  // Function to initialize product page functionality
  function initProductPage() {
    // Element references
    const decreaseBtn = document.getElementById("decrease-qty");
    const increaseBtn = document.getElementById("increase-qty");
    const quantityInput = document.getElementById("quantity");
    const favoriteBtn = document.getElementById("favorite-button");
    const shareBtn = document.getElementById("share-button");
    const addToCartBtn = document.getElementById("add-to-cart");
    const buyNowBtn = document.getElementById("buy-now");

    // First, ensure all utilities are available
    if (window.UtilsInitializer) {
      window.UtilsInitializer.initAllUtils().then(({ favoritesUtils }) => {
        // If favorites utils is initialized, update the favorite button state
        if (favoritesUtils && favoriteBtn) {
          const productId = document.querySelector("#product-id").textContent.trim().split(": ")[1];
          if (favoritesUtils.isFavorite(productId)) {
            const icon = favoriteBtn.querySelector(".material-icons-round");
            const textSpan = favoriteBtn.querySelector("span:not(.material-icons-round)");
            if (icon) {
              icon.textContent = "favorite";
              if (textSpan) textSpan.textContent = "Saved";
              icon.classList.add("text-red-500");
              favoriteBtn.querySelector("div").classList.add("bg-red-50");
            }
          }
        }
      });
    }

    // Get product details from the page
    const productName = document.querySelector("h1").textContent.trim();
    // Get current page URL to extract product ID from path
    const url = new URL(window.location.href);
    const urlParts = url.pathname.split("/");
    const slug = urlParts[urlParts.length - 1];

    // Get price element for display
    const priceElement = document.querySelector(
      ".text-2xl.font-bold.text-gray-900"
    );
    // Price will be used from the product object directly

    // Get product image
    const mainImage = document.querySelector(".flex-shrink-0 img");
    const imageUrl = mainImage ? mainImage.src : "";

    // Get category name if available
    const categoryElement = document.querySelector(
      ".text-xs.text-gray-600.bg-gray-100"
    );
    const categoryName = categoryElement
      ? categoryElement.textContent.trim()
      : "";

    // Create product object to use with favorites
    const productIdElement = document.querySelector("#product-id");
    const product = {
      id: productIdElement ? productIdElement.textContent.trim().split(": ")[1] : "",
      name: productName,
      price: priceElement ? priceElement.textContent.trim() : "",
      image_url: imageUrl,
      image: imageUrl,
      category_name: categoryName,
      url_slug: slug,
      slug: slug // Include both for compatibility
    };

    // Image slider functionality
    const imageSlider = document.getElementById("image-slider");
    const paginationDots = document.querySelectorAll("[data-index]");
    const currentSlideIndicator = document.getElementById("current-slide");
    const prevSlideBtn = document.getElementById("prev-slide");
    const nextSlideBtn = document.getElementById("next-slide");
    const fullscreenToggle = document.getElementById("fullscreen-toggle");
    const fullscreenViewer = document.getElementById("fullscreen-viewer");
    const closeFullscreenBtn = document.getElementById("close-fullscreen");
    const fullscreenSlider = document.getElementById("fullscreen-slider");
    const fullscreenCurrent = document.getElementById("fullscreen-current");

    let currentSlide = 0;
    const slideCount = paginationDots.length;

    // Function to go to a specific slide
    function goToSlide(index, isFullscreen = false) {
      if (index < 0) index = slideCount - 1;
      if (index >= slideCount) index = 0;

      currentSlide = index;

      // Update slider position
      const targetSlider = isFullscreen ? fullscreenSlider : imageSlider;
      targetSlider.style.transform = `translateX(-${currentSlide * 100}%)`;

      // Update indicators
      if (!isFullscreen) {
        currentSlideIndicator.textContent = (currentSlide + 1).toString();

        // Update pagination dots
        paginationDots.forEach((dot, i) => {
          if (i === currentSlide) {
            dot.classList.add("bg-white", "w-6");
            dot.classList.remove("bg-white/50", "w-3");
          } else {
            dot.classList.add("bg-white/50", "w-3");
            dot.classList.remove("bg-white", "w-6");
          }
        });
      } else {
        fullscreenCurrent.textContent = (currentSlide + 1).toString();
      }
    }

    // Add slider navigation
    if (prevSlideBtn) {
      prevSlideBtn.addEventListener("click", () => {
        goToSlide(currentSlide - 1);
        if ("vibrate" in navigator) navigator.vibrate(30);
      });
    }

    if (nextSlideBtn) {
      nextSlideBtn.addEventListener("click", () => {
        goToSlide(currentSlide + 1);
        if ("vibrate" in navigator) navigator.vibrate(30);
      });
    }

    // Add click event to pagination dots
    paginationDots.forEach((dot, index) => {
      dot.addEventListener("click", () => {
        goToSlide(index);
        if ("vibrate" in navigator) navigator.vibrate(30);
      });
    });

    // Fullscreen handling
    if (fullscreenToggle && fullscreenViewer) {
      fullscreenToggle.addEventListener("click", () => {
        fullscreenViewer.classList.remove("hidden");
        document.body.classList.add("overflow-hidden");
        fullscreenSlider.style.transform = `translateX(-${currentSlide * 100}%)`;
        fullscreenCurrent.textContent = (currentSlide + 1).toString();

        setTimeout(() => {
          fullscreenViewer.classList.add("opacity-100");
        }, 10);

        if ("vibrate" in navigator) navigator.vibrate(40);
      });

      closeFullscreenBtn.addEventListener("click", () => {
        document.body.classList.remove("overflow-hidden");
        fullscreenViewer.classList.remove("opacity-100");

        setTimeout(() => {
          fullscreenViewer.classList.add("hidden");
        }, 300);

        if ("vibrate" in navigator) navigator.vibrate(40);
      });
    }

    // Enhanced swipe functionality
    let touchStartX = 0;
    let initialTransform = 0;
    let currentTranslate = 0;
    let isDragging = false;

    // Touch events for main slider
    imageSlider.addEventListener("touchstart", handleTouchStart);
    imageSlider.addEventListener("touchmove", handleTouchMove);
    imageSlider.addEventListener("touchend", handleTouchEnd);

    // Touch events for fullscreen slider
    if (fullscreenSlider) {
      fullscreenSlider.addEventListener("touchstart", (e) =>
        handleTouchStart(e, true)
      );
      fullscreenSlider.addEventListener("touchmove", (e) =>
        handleTouchMove(e, true)
      );
      fullscreenSlider.addEventListener("touchend", (e) =>
        handleTouchEnd(e, true)
      );
    }

    function handleTouchStart(e, isFullscreen = false) {
      touchStartX = e.touches[0].clientX;
      isDragging = true;

      const slider = isFullscreen ? fullscreenSlider : imageSlider;
      initialTransform = getCurrentTranslate(slider);

      slider.style.transition = "none";
    }

    function handleTouchMove(e, isFullscreen = false) {
      if (!isDragging) return;

      const currentPosition = e.touches[0].clientX;
      const diff = currentPosition - touchStartX;

      const slider = isFullscreen ? fullscreenSlider : imageSlider;
      const containerWidth = slider.offsetWidth;

      const movePercentage = (diff / containerWidth) * 100;
      currentTranslate = initialTransform + movePercentage;

      if (currentTranslate > 0) {
        currentTranslate = 0;
      } else if (currentTranslate < -((slideCount - 1) * 100)) {
        currentTranslate = -((slideCount - 1) * 100);
      }

      slider.style.transform = `translateX(${currentTranslate}%)`;
    }

    function handleTouchEnd(_event, isFullscreen = false) {
      if (!isDragging) return;
      isDragging = false;

      const slider = isFullscreen ? fullscreenSlider : imageSlider;
      slider.style.transition = "transform 300ms ease-out";

      const movedPercentage = currentTranslate - initialTransform;
      const threshold = 15; // 15%

      let newIndex;
      if (movedPercentage < -threshold) {
        newIndex = Math.min(currentSlide + 1, slideCount - 1);
      } else if (movedPercentage > threshold) {
        newIndex = Math.max(currentSlide - 1, 0);
      } else {
        newIndex = currentSlide;
      }

      goToSlide(newIndex, isFullscreen);
    }

    function getCurrentTranslate(element) {
      const style = window.getComputedStyle(element);
      const matrix = new WebKitCSSMatrix(style.transform);
      const elementWidth = element.offsetWidth;
      return (matrix.m41 / elementWidth) * 100;
    }

    // Double tap zoom
    let lastTap = 0;
    imageSlider.addEventListener("click", function (e) {
      const currentTime = new Date().getTime();
      const tapLength = currentTime - lastTap;

      if (tapLength < 300 && tapLength > 0) {
        if (fullscreenToggle) fullscreenToggle.click();
        e.preventDefault();
      }

      lastTap = currentTime;
    });

    // Quantity modification with improved UX
    if (decreaseBtn && increaseBtn && quantityInput) {
      decreaseBtn.addEventListener("click", () => {
        let currentQty = parseInt(quantityInput.value) || 1;
        if (currentQty > 1) {
          quantityInput.value = currentQty - 1;
          updateCartButtonText();
        }
        if ("vibrate" in navigator) navigator.vibrate(30);
      });

      increaseBtn.addEventListener("click", () => {
        let currentQty = parseInt(quantityInput.value) || 1;
        quantityInput.value = currentQty + 1;
        updateCartButtonText();
        if ("vibrate" in navigator) navigator.vibrate(30);
      });

      // Ensure input is always valid
      quantityInput.addEventListener("change", () => {
        let value = parseInt(quantityInput.value) || 1;
        if (value < 1) value = 1;
        quantityInput.value = value;
        updateCartButtonText();
      });
    }

    // Update "Add to Cart" button text based on quantity
    function updateCartButtonText() {
      const quantity = parseInt(quantityInput.value) || 1;
      if (quantity > 1) {
        addToCartBtn.innerHTML = `
          <span class="material-icons-round text-sm">shopping_cart</span>
          <span>Add ${quantity}</span>`;
      } else {
        addToCartBtn.innerHTML = `
          <span class="material-icons-round text-sm">shopping_cart</span>
          <span>Add</span>`;
      }
    }

    // Add to cart functionality with modern UI feedback
    if (addToCartBtn) {
      addToCartBtn.addEventListener("click", () => {
        const quantity = parseInt(quantityInput.value) || 1;
        const productName = document.querySelector("h1").textContent.trim();

        // Get current page URL to extract product ID from path
        const url = new URL(window.location.href);
        const urlParts = url.pathname.split("/");
        const slug = urlParts[urlParts.length - 1];

        // Get price without the currency symbol
        const priceElement = document.querySelector(
          ".text-2xl.font-bold.text-gray-900"
        );
        let price = 0;
        if (priceElement) {
          const priceText = priceElement.textContent.trim();
          // Support both $ and ₹ price formats
          price = parseFloat(priceText.replace(/[₹$]/g, ""));
        }

        // Get product image
        const mainImage = document.querySelector(".flex-shrink-0 img");
        const imageUrl = mainImage ? mainImage.src : "";

        // Get unit information
        const unitBadge = document.querySelector("#unit_type");
        const unit_valueBad = document.querySelector("#unit_value");
        let unit_type = "quantity";
        let unit_value = 1;

        if (unitBadge) {
          const unitText = unitBadge.textContent.trim();
          unit_type = unitText;
        }
        if (unit_valueBad) {
          const unitValueText = unit_valueBad.textContent.trim();
          unit_value = parseInt(unitValueText) || 1;
        }
        // Create cart item object
        const cartItem = {
          id: product.id || slug,
          name: productName,
          price: price,
          quantity: quantity,
          image: imageUrl,
          image_url: imageUrl,
          slug: slug,
          unit_type: unit_type,
          unit_value: unit_value,
        };

        // Add item to cart and update UI
        if (window.CartUtils) {
          // Use CartUtils to add to cart
          window.CartUtils.addToCart(cartItem);

          // Modern visual feedback
          const originalHTML = addToCartBtn.innerHTML;
          const originalClass = addToCartBtn.className;

          // Change button to success state
          addToCartBtn.innerHTML =
            '<span class="material-icons-round">check</span>';
          addToCartBtn.className = addToCartBtn.className.replace(
            "bg-[#FF6B35]",
            "bg-green-500"
          );

          // Return to original state after animation
          setTimeout(() => {
            addToCartBtn.className = originalClass;
            addToCartBtn.innerHTML = originalHTML;
          }, 1500);
        } else if (window.UtilsInitializer) {
          // Try to initialize CartUtils and then add to cart
          window.UtilsInitializer.initCartUtils().then(cartUtils => {
            if (cartUtils) {
              cartUtils.addToCart(cartItem);

              // Modern visual feedback
              const originalHTML = addToCartBtn.innerHTML;
              const originalClass = addToCartBtn.className;

              // Change button to success state
              addToCartBtn.innerHTML =
                '<span class="material-icons-round">check</span>';
              addToCartBtn.className = addToCartBtn.className.replace(
                "bg-[#FF6B35]",
                "bg-green-500"
              );

              // Return to original state after animation
              setTimeout(() => {
                addToCartBtn.className = originalClass;
                addToCartBtn.innerHTML = originalHTML;
              }, 1500);
            } else {
              showToast("Failed to initialize cart. Please try again.");
            }
          });
        } else {
          // Fallback: store in localStorage directly
          const cartKey = "snackswift_cart";
          try {
            // Get existing cart
            const existingCartJSON = localStorage.getItem(cartKey);
            let cart = existingCartJSON ? JSON.parse(existingCartJSON) : [];

            // Check if product already in cart
            const existingItemIndex = cart.findIndex(
              (item) => item.id === slug
            );

            if (existingItemIndex !== -1) {
              // Update quantity if item exists
              cart[existingItemIndex].quantity += quantity;
            } else {
              // Add new item
              cart.push(cartItem);
            }

            // Save updated cart
            localStorage.setItem(cartKey, JSON.stringify(cart));

            // Show success UI
            addToCartBtn.classList.add("bg-green-500");
            addToCartBtn.innerHTML =
              '<span class="material-icons-round">check</span>';

            setTimeout(() => {
              addToCartBtn.classList.remove("bg-green-500");
              addToCartBtn.classList.add("bg-[#FF6B35]");
              addToCartBtn.innerHTML = `
                <span class="material-icons-round text-sm">shopping_cart</span>
                <span>Add</span>`;
            }, 1500);
          } catch (error) {
            console.error("Error updating cart:", error);
          }
        }

        // Enhanced toast notification
        showToast(`${quantity} × ${productName} added to cart`);

        if ("vibrate" in navigator) navigator.vibrate(80);
      });
    }

    // Buy now button functionality
    if (buyNowBtn) {
      buyNowBtn.addEventListener("click", () => {
        // First add to cart
        if (addToCartBtn) {
          addToCartBtn.click();
        }

        // Then redirect to checkout
        setTimeout(() => {
          window.location.href = "/checkout";
        }, 300);
      });
    }

    // Enhanced favorite button toggle with modern animation
    if (favoriteBtn) {
      // Check if product is already in favorites
      if (window.FavoritesUtils) {
        console.log('Checking if product is in favorites, ID:', product.id);
        const isFav = window.FavoritesUtils.isFavorite(product.id);
        console.log('Is product in favorites?', isFav);

        if (isFav) {
          const icon = favoriteBtn.querySelector(".material-icons-round");
          const textSpan = favoriteBtn.querySelector(
            "span:not(.material-icons-round)"
          );

          if (icon) {
            icon.textContent = "favorite";
            if (textSpan) textSpan.textContent = "Saved";
            icon.classList.add("text-red-500");
            favoriteBtn.querySelector("div").classList.add("bg-red-50");
          }
        }
      }

      favoriteBtn.addEventListener("click", () => {
        console.log('Favorite button clicked');
        const icon = favoriteBtn.querySelector(".material-icons-round");
        const textSpan = favoriteBtn.querySelector(
          "span:not(.material-icons-round)"
        );
        if (!icon) return;

        // Make sure product object has the correct ID
        if (product.id === undefined || product.id === null) {
          product.id = document.querySelector("#product-id").textContent.trim().split(": ")[1];
        }

        console.log('Product object for favorites:', product);

        if (window.FavoritesUtils) {
          console.log('Using FavoritesUtils directly');
          // Toggle favorite status using the utility
          const isAdded = window.FavoritesUtils.toggleFavorite(product);
          console.log('Toggle result:', isAdded);
          updateFavoriteUI(isAdded, icon, textSpan, favoriteBtn);
        } else if (window.UtilsInitializer) {
          console.log('Using UtilsInitializer to get FavoritesUtils');
          // Try to initialize FavoritesUtils and then toggle favorite
          window.UtilsInitializer.initFavoritesUtils().then(favoritesUtils => {
            if (favoritesUtils) {
              console.log('FavoritesUtils initialized successfully');
              const isAdded = favoritesUtils.toggleFavorite(product);
              console.log('Toggle result:', isAdded);
              updateFavoriteUI(isAdded, icon, textSpan, favoriteBtn);
            } else {
              console.error('Failed to initialize FavoritesUtils');
              showToast("Failed to initialize favorites. Please try again.");
            }
          });
        } else {
          console.error("FavoritesUtils not available");
          showToast("Unable to save favorite. Please try again later.");
        }

        // Helper function to update favorite UI
        function updateFavoriteUI(isAdded, icon, textSpan, favoriteBtn) {
          console.log('Updating favorite UI, isAdded:', isAdded);

          if (isAdded) {
            icon.textContent = "favorite";
            if (textSpan) textSpan.textContent = "Saved";
            icon.classList.add("text-red-500");
            favoriteBtn.querySelector("div").classList.add("bg-red-50");

            // Add heart animation
            const heart = document.createElement("div");
            heart.className =
              "absolute animate-ping w-5 h-5 bg-red-500 rounded-full opacity-75";
            favoriteBtn.querySelector("div").appendChild(heart);
            setTimeout(() => heart.remove(), 700);

            showToast("Added to favorites");
          } else {
            icon.textContent = "favorite_border";
            if (textSpan) textSpan.textContent = "Save";
            icon.classList.remove("text-red-500");
            favoriteBtn.querySelector("div").classList.remove("bg-red-50");
            showToast("Removed from favorites");
          }

          // Dispatch custom event for any listeners
          console.log('Dispatching favorites-updated event');
          try {
            window.dispatchEvent(new CustomEvent("favorites-updated"));
          } catch (error) {
            console.error('Error dispatching favorites-updated event:', error);
          }
        }

        if ("vibrate" in navigator) navigator.vibrate(50);
      });
    }

    // Enhanced share functionality
    if (shareBtn) {
      shareBtn.addEventListener("click", () => {
        if (navigator.share) {
          navigator
            .share({
              title: document.title,
              url: window.location.href,
            })
            .catch(console.error);
        } else {
          navigator.clipboard.writeText(window.location.href).then(() => {
            shareBtn.querySelector("div").classList.add("bg-blue-100");
            shareBtn.querySelector(".material-icons-round").textContent =
              "check";

            setTimeout(() => {
              shareBtn.querySelector("div").classList.remove("bg-blue-100");
              shareBtn.querySelector(".material-icons-round").textContent =
                "share";
            }, 1500);

            showToast("Link copied to clipboard");
          });
        }

        if ("vibrate" in navigator) navigator.vibrate(50);
      });
    }

    // Enhanced toast notification with modern design
    function showToast(message) {
      let toastContainer = document.querySelector(".toast-container");

      if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.className =
          "toast-container fixed bottom-24 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-5";
        document.body.appendChild(toastContainer);
      }

      const toast = document.createElement("div");
      toast.className =
        "bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-xl opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md";

      let icon = "info";

      if (message.includes("added")) {
        icon = "shopping_cart";
      } else if (message.includes("favorite") || message.includes("Saved")) {
        icon = "favorite";
      } else if (message.includes("copied") || message.includes("link")) {
        icon = "content_copy";
      }

      toast.innerHTML = `
        <span class="material-icons-round text-base mr-2">${icon}</span>
        <span>${message}</span>
      `;

      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove("opacity-0", "translate-y-4");
        toast.classList.add("opacity-95");
      }, 10);

      setTimeout(() => {
        toast.classList.add("opacity-0", "translate-y-4");
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 2500);
    }

    // Initialize UI
    updateCartButtonText();
  }

  // Initialize on DOMContentLoaded (first page load)
  document.addEventListener("DOMContentLoaded", initProductPage);

  // Initialize on Astro page transitions
  // document.addEventListener("astro:page-load", initProductPage);
</script>

<style is:inline>
  /* Hide scrollbars but allow scrolling */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Fullscreen viewer transitions */
  #fullscreen-viewer {
    transition: opacity 0.3s ease;
    opacity: 0;
  }

  /* Slider optimizations for mobile */
  #image-slider,
  #fullscreen-slider {
    will-change: transform;
    -webkit-overflow-scrolling: touch;
    touch-action: pan-x;
  }

  /* Prevent blue highlight on mobile tap */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Add material design card effect */
  .bg-white {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* Smooth button transitions */
  button {
    transition: all 0.2s ease;
  }

  /* Add pinch-zoom support */
  .pinch-zoom {
    overflow: hidden;
    touch-action: none;
    will-change: transform;
  }

  /* Enhance button press effect */
  button:active:not(:disabled) {
    transform: scale(0.97);
  }
</style>
