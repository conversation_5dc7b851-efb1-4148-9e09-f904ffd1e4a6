---
import MainLayout from "../layouts/MainLayout.astro";
---

<MainLayout title="My Favorites">
  <div class="container mx-auto px-4 py-6">
    <div class="flex items-center mb-6">
      <h1 class="text-2xl font-bold">My Favorites</h1>
      <div class="ml-2">
        <span class="material-icons-round text-red-500">favorite</span>
      </div>
    </div>

    <div id="favorites-container" class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
      <!-- Favorites will be loaded here -->
      <div class="col-span-full text-center py-10 hidden" id="empty-favorites">
        <div class="flex flex-col items-center">
          <span class="material-icons-round text-6xl text-gray-300 mb-4">favorite_border</span>
          <p class="text-gray-500 font-medium">No favorites yet</p>
          <p class="text-gray-400 text-sm mt-2">Items you save will appear here</p>
          <a href="/" class="mt-6 bg-[#FF6B35] text-white py-2 px-6 rounded-lg shadow-lg shadow-orange-200 flex items-center">
            <span class="material-icons-round mr-2 text-sm">restaurant_menu</span>
            Browse Menu
          </a>
        </div>
      </div>
    </div>
  </div>

<!-- Ensure favorites utils is loaded -->
<script is:inline src="/scripts/favorites-utils.js"></script>

<script is:inline>
  // Add debug logging
  console.log('Favorites script loaded');

  // Function to initialize favorites page
  function initFavoritesPage() {
    console.log('Initializing favorites page');
    const favoritesContainer = document.getElementById('favorites-container');
    const emptyFavorites = document.getElementById('empty-favorites');

    if (!favoritesContainer) {
      console.error('Favorites container not found');
      return;
    }

    console.log('Favorites container found:', favoritesContainer);

    // Check if FavoritesUtils is already available
    if (window.FavoritesUtils) {
      console.log('FavoritesUtils already available, loading favorites');
      loadFavorites();
    } else {
      console.log('FavoritesUtils not available, waiting for it to load');
      // Listen for the favorites-utils-ready event
      window.addEventListener('favorites-utils-ready', function() {
        console.log('FavoritesUtils is now ready, loading favorites');
        loadFavorites();
      });
    }

    // Make sure FavoritesUtils is loaded
    const loadFavoritesUtils = () => {
      return new Promise((resolve) => {
        if (window.FavoritesUtils) {
          resolve(window.FavoritesUtils);
        } else {
          // Create a script element if FavoritesUtils isn't available
          const script = document.createElement('script');
          script.src = '/scripts/favorites-utils.js';
          script.onload = () => {
            if (window.FavoritesUtils) {
              resolve(window.FavoritesUtils);
            } else {
              resolve(null); // Failed to load
            }
          };
          script.onerror = () => resolve(null);
          document.head.appendChild(script);
        }
      });
    };

    async function loadFavorites() {
      console.log('loadFavorites function called');

      // Try to get FavoritesUtils directly first
      let favUtils = window.FavoritesUtils;

      // If not available, try to load it
      if (!favUtils) {
        console.log('FavoritesUtils not directly available, trying to load it');
        favUtils = await loadFavoritesUtils();
      }

      if (!favUtils) {
        console.error('FavoritesUtils not found');
        showError('Unable to load favorites. Please refresh the page.');
        return;
      }

      console.log('FavoritesUtils loaded successfully');
      const favorites = favUtils.getFavorites();
      console.log('Loaded favorites:', favorites);

      if (!favorites || favorites.length === 0) {
        console.log('No favorites found, showing empty state');
        if (emptyFavorites) {
          emptyFavorites.classList.remove('hidden');
        }
        return;
      }

      console.log('Found favorites, hiding empty state');
      if (emptyFavorites) {
        emptyFavorites.classList.add('hidden');
      }

      // Clear previous content except the empty state message
      console.log('Clearing previous content');
      const children = Array.from(favoritesContainer.children);
      children.forEach(child => {
        if (child.id !== 'empty-favorites') {
          child.remove();
        }
      });

      // Add each favorite item to the grid
      favorites.forEach(product => {
        console.log('Adding product to favorites:', product);
        const card = document.createElement('div');
        card.className = 'bg-white rounded-xl shadow-md overflow-hidden relative';
        card.dataset.id = product.id;

        // Use url_slug if available, otherwise fall back to slug
        const productSlug = product.url_slug || product.slug || product.id;

        card.innerHTML = `
          <a href="/product/${productSlug}" class="block">
            <div class="h-40 overflow-hidden relative">
              <img
                src="${product.image_url || '/images/placeholder-food.jpg'}"
                alt="${product.name}"
                class="w-full h-full object-cover"
              />
              ${product.category_name ? `<span class="absolute top-2 left-2 px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded-lg">
                ${product.category_name}
              </span>` : ''}
            </div>
            <div class="p-3">
              <h3 class="font-medium text-gray-900 line-clamp-1">${product.name}</h3>
              <p class="font-bold mt-1">${product.price}</p>
            </div>
          </a>
          <button class="remove-favorite absolute top-2 right-2 bg-white rounded-full p-1 shadow-md">
            <span class="material-icons-round text-red-500 text-lg">favorite</span>
          </button>
        `;

        favoritesContainer.appendChild(card);
      });

      // Add event listeners to remove buttons
      document.querySelectorAll('.remove-favorite').forEach(button => {
        button.addEventListener('click', async (e) => {
          e.preventDefault();
          const card = button.closest('[data-id]');
          const productId = card.dataset.id;

          const favUtils = await loadFavoritesUtils();
          if (!favUtils) {
            showToast('Unable to remove favorite. Please try again.');
            return;
          }

          // Remove from favorites
          favUtils.removeFavorite(productId);

          // Animate the removal
          card.classList.add('opacity-0', 'scale-95');
          card.style.transition = 'all 300ms ease-out';

          setTimeout(() => {
            card.remove();
            if (favUtils.getFavorites().length === 0 && emptyFavorites) {
              emptyFavorites.classList.remove('hidden');
            }
          }, 300);

          // Show toast
          showToast('Removed from favorites');

          if ('vibrate' in navigator) {
            navigator.vibrate(40);
          }
        });
      });
    }

    // Display toast messages
    function showToast(message) {
      let toastContainer = document.querySelector('.toast-container');

      if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container fixed bottom-24 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-5';
        document.body.appendChild(toastContainer);
      }

      const toast = document.createElement('div');
      toast.className = 'bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-xl opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md';

      toast.innerHTML = `
        <span class="material-icons-round text-base mr-2">favorite</span>
        <span>${message}</span>
      `;

      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove('opacity-0', 'translate-y-4');
        toast.classList.add('opacity-95');
      }, 10);

      setTimeout(() => {
        toast.classList.add('opacity-0', 'translate-y-4');
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 2500);
    }

    function showError(message) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'col-span-full text-center py-6 text-red-500';
      errorDiv.innerHTML = `
        <span class="material-icons-round text-3xl mb-2">error_outline</span>
        <p>${message}</p>
      `;

      // Clear previous content except the empty state message
      const children = Array.from(favoritesContainer.children);
      children.forEach(child => {
        if (child.id !== 'empty-favorites') {
          child.remove();
        }
      });

      if (emptyFavorites) {
        emptyFavorites.classList.add('hidden');
      }
      favoritesContainer.appendChild(errorDiv);
    }

    // Load favorites when page loads
    loadFavorites();

    // Listen for changes in favorites
    window.addEventListener('favorites-updated', loadFavorites);
  }

  // Initialize on DOMContentLoaded
  document.addEventListener('DOMContentLoaded', initFavoritesPage);

  // Initialize on Astro page transitions
  document.addEventListener('astro:page-load', initFavoritesPage);
</script>
</MainLayout>