---
import MainLayout from "../layouts/MainLayout.astro";
import { 
  getCategories, 
  getFeaturedProducts, 
  getPromotions, 
  getPopularProducts, 
  getFirstUser 
} from "../db/database";

// Disable prerendering to use server-side rendering with Cloudflare D1
export const prerender = false;

// Define fallback data
const fallbackCategories = [
  { id: 1, name: "<PERSON><PERSON>", icon: "🍟", color: "#FF9F80" },
  { id: 2, name: "Cook<PERSON>", icon: "🍪", color: "#E0C094" },
  { id: 3, name: "Chocolates", icon: "🍫", color: "#C79F7A" },
  { id: 4, name: "Drinks", icon: "🥤", color: "#8ECAE6" },
];

// Safely fetch all required data for the homepage
 let featuredProducts = [];
 let popularItems = [];
let user = { name: "Guest", points: 0, level: "Bronze" };

try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
     featuredProducts = await getFeaturedProducts(Astro.locals.runtime.env) || [];
     popularItems = await getPopularProducts(Astro.locals.runtime.env) || [];
    user = await getFirstUser(Astro.locals.runtime.env) || user;
  } else {
    console.warn("Runtime environment not available, using fallback data");
   }
} catch (error) {
  console.error("Error fetching data:", error);
 }
---

<MainLayout showHeader={true} showBackButton={false}>
  <div class="px-4 py-2 max-w-md mx-auto">
    <!-- Greeting Section -->
    <section class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-semibold">
          Hello, <span class="text-[#FF6B35]">{user.name}</span> 👋
        </h1>
      </div>
    </section>

    <!-- Featured Products Carousel -->
    <section class="mb-8">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">Featured Products</h2>
        <a href="/products" class="text-sm text-[#FF6B35] font-medium">
          View all
        </a>
      </div>

      <div
        class="relative h-56 w-full overflow-hidden rounded-xl shadow-md"
        id="featured-carousel"
      >
        <div class="h-full w-full">
          {
            featuredProducts.map((product, index) => (
              <div class={`carousel-item ${index === 0 ? "active" : ""}`}>
                <div
                  class="h-full w-full bg-cover bg-center p-6 flex flex-col justify-end"
                  style={`background-image: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.7)), url(${product.image})`}
                >
                  {product.is_new && (
                    <div class="absolute top-4 right-4 bg-[#2EC4B6] text-white text-xs font-semibold px-3 py-1 rounded-full">
                      New
                    </div>
                  )}
                  {product.is_on_sale && (
                    <div class="absolute top-4 left-4 bg-[#FFBC42] text-gray-800 text-xs font-semibold px-3 py-1 rounded-full">
                      Sale
                    </div>
                  )}
                  <div class="text-white">
                    <h3 class="text-xl font-semibold mb-1">{product.name}</h3>
                    <p class="text-sm text-white/90 mb-3">
                      {product.description}
                    </p>
                    <div class="flex items-center gap-2 mb-4">
                      <p class="text-lg font-bold">{product.price}</p>
                      {product.old_price && (
                        <p class="text-sm line-through opacity-70">
                          {product.old_price}
                        </p>
                      )}
                    </div>
                    <button class="bg-[#FF6B35] text-white py-2 px-4 rounded-full flex items-center gap-2 text-sm font-medium">
                      <span class="material-icons-round text-sm">
                        add_shopping_cart
                      </span>
                      Add to Cart
                    </button>
                  </div>
                </div>
              </div>
            ))
          }
        </div>
        <div
          class="absolute bottom-2 left-0 right-0 flex justify-center gap-1.5 z-10"
        >
          {
            featuredProducts.map((_, index) => (
              <div
                class={`carousel-indicator ${index === 0 ? "active bg-white" : "bg-white/50"}`}
              />
            ))
          }
        </div>
      </div>
    </section>

     



    <!-- Popular Items -->
    <section class="mb-8">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">Popular Items</h2>
        <a href="/products" class="text-sm text-[#FF6B35] font-medium">
          View all
        </a>
      </div>

      <div class="grid grid-cols-2 gap-4">
        {
          popularItems.map((item) => (
            <a
              href={`/products/${item.url_slug}`}
              class="bg-white rounded-lg overflow-hidden shadow-sm transition active:translate-y-[-2px] active:shadow-md"
            >
              <div class="relative">
                <img
                  class="w-full aspect-square object-cover"
                  src={item.image}
                  alt={item.name}
                />
                <button class="absolute bottom-2 right-2 w-9 h-9 rounded-full bg-[#FF6B35] text-white flex items-center justify-center shadow-sm">
                  <span class="material-icons-round text-lg">add</span>
                </button>
              </div>
              <div class="p-3">
                <h3 class="text-sm font-medium text-gray-800 line-clamp-2 leading-snug mb-2">
                  {item.name}
                </h3>
                <div class="flex justify-between items-center">
                  <p class="text-[#FF6B35] font-semibold">{item.price}</p>
                  <div class="flex items-center gap-0.5 text-xs text-gray-500">
                    <span class="font-semibold">{item.rating}</span>
                    <span class="text-[#FFBC42] text-sm">★</span>
                  </div>
                </div>
              </div>
            </a>
          ))
        }
      </div>
    </section>
  </div>
</MainLayout>
