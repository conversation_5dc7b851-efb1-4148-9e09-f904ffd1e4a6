import React, { useState, useEffect } from "react";

export default function OrderDetail({ orderId }) {
  const [order, setOrder] = useState(null);
  const [orderItems, setOrderItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [statusUpdateSuccess, setStatusUpdateSuccess] = useState(false);
  const [printMode, setPrintMode] = useState(false);
  // Fetch order details on component mount or when orderId changes
  useEffect(() => {
    async function fetchOrderDetails() {
      if (!orderId) return;

      try {
        setLoading(true);
        setError(null);

        const orderDetails = await window.ApiClient.getAdminOrderDetails(
          orderId
        );

        if (orderDetails.order) {
          setOrder(orderDetails.order);
          setOrderItems(orderDetails.order.items || []);
        } else {
          throw new Error("Order not found");
        }
      } catch (err) {
        console.error("Error fetching order details:", err);
        setError(
          `Failed to load order details: ${err.message || "Unknown error"}`
        );
      } finally {
        setLoading(false);
      }
    }

    fetchOrderDetails();
  }, [orderId]);

  // Handle order status update
  const handleStatusChange = async (newStatus) => {
    try {
      setIsUpdatingStatus(true);
      setStatusUpdateSuccess(false);

      // Call API to update order status
      const result = await window.ApiClient.updateOrderStatus(
        orderId,
        newStatus
      );

      if (result.success) {
        // Update the order in state
        setOrder((prev) => ({
          ...prev,
          order_status: newStatus,
        }));
        setStatusUpdateSuccess(true);

        // Clear success message after a few seconds
        setTimeout(() => {
          setStatusUpdateSuccess(false);
        }, 3000);
      } else {
        throw new Error(result.message || "Failed to update status");
      }
    } catch (err) {
      console.error(`Error updating order status:`, err);
      setError(
        `Failed to update order status: ${err.message || "Unknown error"}`
      );

      // Clear error after a few seconds
      setTimeout(() => {
        setError(null);
      }, 4000);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Format currency values
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Format dates
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Generate order status label with appropriate colors
  const getStatusLabel = (status) => {
    const statusColors = {
      placed: "bg-purple-100 text-purple-800",
      processing: "bg-blue-100 text-blue-800",
      shipped: "bg-indigo-100 text-indigo-800",
      delivered: "bg-green-100 text-green-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
          statusColors[status] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status?.charAt(0).toUpperCase() + status?.slice(1) || "Unknown"}
      </span>
    );
  };

  // Handle printing invoice
  const handlePrintInvoice = () => {
    // Add print mode class to body
    document.body.classList.add("print-mode");
    setPrintMode(true);
    // Create a small delay to ensure styles are applied
    setTimeout(() => {
      window.print();

      // Remove print mode class after printing
      window.addEventListener(
        "afterprint",
        () => {
          document.body.classList.remove("print-mode");
          setPrintMode(false);
        },
        { once: true }
      );

      // Fallback in case afterprint event doesn't fire
      setTimeout(() => {
        document.body.classList.remove("print-mode");
        setPrintMode(false);
      }, 2000);
    }, 100);
  };

  // Display loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
        <p className="text-gray-600">Loading order details...</p>
      </div>
    );
  }

  // Display error state
  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <span className="material-icons-round text-red-400">
              error_outline
            </span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <button
                onClick={() => window.history.back()}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <span className="material-icons-round mr-1 text-sm">
                  arrow_back
                </span>
                Back to Orders
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Display if order is not found
  if (!order) {
    return (
      <div className="text-center py-12">
        <span className="material-icons-round text-gray-400 text-5xl mb-4">
          receipt_long
        </span>
        <h2 className="text-xl font-medium text-gray-900 mb-2">
          Order Not Found
        </h2>
        <p className="text-gray-500 mb-6">
          The order you're looking for doesn't exist or you don't have
          permission to view it.
        </p>
        <button
          onClick={() => window.history.back()}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <span className="material-icons-round mr-2">arrow_back</span>
          Back to Orders
        </button>
      </div>
    );
  }

  return (
    <>
      {!printMode && (
        <div className="space-y-6 pb-10">
          {/* Header with back button and status display */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
            <div className="flex items-center mb-4 sm:mb-0">
              <button
                onClick={() => window.history.back()}
                className="mr-4 text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <span className="material-icons-round">arrow_back</span>
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Order #{order.order_number}
                </h1>
                <p className="text-gray-500">
                  Placed on {formatDate(order.created_at)}
                </p>
              </div>
            </div>
            <div className="print:hidden">
              <button
                onClick={handlePrintInvoice}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                <span className="material-icons-round mr-2">print</span>
                Print Invoice
              </button>
            </div>
          </div>

          {/* Status update success message */}
          {statusUpdateSuccess && (
            <div className="rounded-md bg-green-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <span className="material-icons-round text-green-400">
                    check_circle
                  </span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800">
                    Order status updated successfully
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Order Information Grid */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Left Column - Order Details */}
            <div className="space-y-6">
              {/* Order Status Section */}
              <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Order Status
                </h2>

                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Current Status:
                    </span>
                    {getStatusLabel(order.order_status)}
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Payment Status:
                    </span>
                    <span
                      className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                        order.payment_status === "paid"
                          ? "bg-green-100 text-green-800"
                          : order.payment_status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {order.payment_status?.charAt(0).toUpperCase() +
                        order.payment_status?.slice(1) || "Unknown"}
                    </span>
                  </div>
                </div>

                <div className="print:hidden">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Update Status:
                  </label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {[
                      "placed",
                      "processing",
                      "shipped",
                      "delivered",
                      "cancelled",
                    ].map((status) => (
                      <button
                        key={status}
                        onClick={() => handleStatusChange(status)}
                        disabled={
                          isUpdatingStatus || order.order_status === status
                        }
                        className={`py-2 px-3 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 ${
                          order.order_status === status
                            ? "bg-gray-100 text-gray-800 font-medium cursor-default"
                            : status === "cancelled"
                            ? "bg-red-50 text-red-700 hover:bg-red-100"
                            : status === "delivered"
                            ? "bg-green-50 text-green-700 hover:bg-green-100"
                            : "bg-blue-50 text-blue-700 hover:bg-blue-100"
                        } ${
                          isUpdatingStatus
                            ? "opacity-50 cursor-not-allowed"
                            : ""
                        }`}
                      >
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </button>
                    ))}
                  </div>
                  {isUpdatingStatus && (
                    <div className="flex justify-center mt-3">
                      <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-orange-500"></div>
                    </div>
                  )}
                </div>
              </section>

              {/* Customer Information */}
              <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Customer Information
                </h2>
                <div className="space-y-3">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Name</h3>
                    <p className="mt-1">{order.customer.name || "N/A"}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Email</h3>
                    <p className="mt-1">{order.customer.email || "N/A"}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                    <p className="mt-1">{order.customer.phone || "N/A"}</p>
                  </div>
                </div>
              </section>

              {/* Shipping Information */}
              <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Shipping Information
                </h2>
                <div className="space-y-3">
                  {order.address ? (
                    <>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">
                          Address
                        </h3>
                        <p className="mt-1">{order.address.address}</p>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">
                            City
                          </h3>
                          <p className="mt-1">{order.address.city}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">
                            ZIP Code
                          </h3>
                          <p className="mt-1">{order.address.zip_code}</p>
                        </div>
                      </div>
                      {order.address.instructions && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">
                            Special Instructions
                          </h3>
                          <p className="mt-1 text-sm text-gray-700">
                            {order.address.instructions}
                          </p>
                        </div>
                      )}
                    </>
                  ) : (
                    <p className="text-gray-500">
                      No shipping information available
                    </p>
                  )}

                  {order.location && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <h3 className="text-sm font-medium text-gray-500">
                        Delivery Location
                      </h3>
                      <p className="mt-1">{order.location.name}</p>
                      <p className="mt-1 text-sm text-gray-500">
                        {order.location.address}
                      </p>
                    </div>
                  )}
                </div>
              </section>
            </div>

            {/* Right Column - Items & Payment */}
            <div className="space-y-6">
              {/* Order Items */}
              <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Order Items
                </h2>
                {orderItems.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {orderItems.map((item) => (
                      <div key={item.id} className="py-4 flex">
                        {item.product_image && (
                          <div className="flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border border-gray-200">
                            <img
                              src={item.product_image}
                              alt={item.product_name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                        <div
                          className={`${
                            item.product_image ? "ml-4" : ""
                          } flex-1`}
                        >
                          <h3 className="text-sm font-medium text-gray-900">
                            {item.product_name}
                          </h3>
                          {/* Display unit information if available */}
                          {item.unit_type && item.unit_value && (
                            <p className="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-md inline-block mt-1">
                              {item.unit_type === "quantity"
                                ? `${item.unit_value} ${
                                    item.unit_value > 1 ? "items" : "item"
                                  }`
                                : `${item.unit_value}${item.unit_type}`}
                            </p>
                          )}
                          <div className="flex justify-between mt-1">
                            <p className="text-sm text-gray-500">
                              {item.quantity} ×{" "}
                              {formatCurrency(item.product_price)}
                            </p>
                            <p className="text-sm font-medium text-gray-900">
                              {formatCurrency(item.total_price)}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No items found for this order</p>
                )}
              </section>

              {/* Payment Information */}
              <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Payment Information
                </h2>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Payment Method
                      </h3>
                      <p className="mt-1 capitalize">
                        {order.payment_method || "N/A"}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Payment Status
                      </h3>
                      <p className="mt-1">
                        <span
                          className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                            order.payment_status === "paid"
                              ? "bg-green-100 text-green-800"
                              : order.payment_status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {order.payment_status?.charAt(0).toUpperCase() +
                            order.payment_status?.slice(1) || "Unknown"}
                        </span>
                      </p>
                    </div>
                  </div>
                  {order.payment_id && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Payment ID
                      </h3>
                      <p className="mt-1">{order.payment_id}</p>
                    </div>
                  )}
                </div>
              </section>

              {/* Order Summary */}
              <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Order Summary
                </h2>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <p className="text-gray-500">Subtotal</p>
                    <p className="text-gray-900">
                      {formatCurrency(
                        order.total_amount -
                          (order.delivery_fee || 0) +
                          (order.discount_amount || 0)
                      )}
                    </p>
                  </div>
                  <div className="flex justify-between text-sm">
                    <p className="text-gray-500">Delivery Fee</p>
                    <p className="text-gray-900">
                      {formatCurrency(order.delivery_fee || 0)}
                    </p>
                  </div>
                  {order.discount_amount > 0 && (
                    <div className="flex justify-between text-sm">
                      <p className="text-green-600 flex items-center">
                        <span className="material-icons-round text-xs mr-1">
                          local_offer
                        </span>
                        Discount{" "}
                        {order.coupon_code ? `(${order.coupon_code})` : ""}
                      </p>
                      <p className="text-green-600">
                        -{formatCurrency(order.discount_amount || 0)}
                      </p>
                    </div>
                  )}
                  <div className="border-t border-gray-200 mt-4 pt-4 flex justify-between">
                    <p className="text-base font-medium text-gray-900">Total</p>
                    <p className="text-base font-medium text-gray-900">
                      {formatCurrency(order.total_amount || 0)}
                    </p>
                  </div>
                </div>
              </section>

              {/* Additional Notes - only show if there's content */}
              {order.cancel_reason && (
                <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">
                    Additional Information
                  </h2>
                  <div className="space-y-3">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Cancellation Reason
                      </h3>
                      <p className="mt-1 text-sm text-red-600">
                        {order.cancel_reason}
                      </p>
                    </div>
                  </div>
                </section>
              )}
            </div>
          </div>
        </div>
      )}

      {order && (
        <style jsx global>{`
          /* Print-specific styles for A4 paper */
          @media print {
            /* Set A4 page size and margins */
            @page {
              size: A4 portrait !important; /* 210mm × 297mm */
              margin: 10mm !important; /* 1cm margin on all sides */
            }

            /* Reset body styles for printing */
            body {
              background-color: white !important;
              padding: 0 !important;
              margin: 0 !important;
              color: #000 !important;
              font-size: 9pt !important;
              line-height: 1.2 !important;
              position: static !important;
              overflow: visible !important;
              height: auto !important;
              width: 190mm !important; /* A4 width minus margins */
            }

            /* Hide non-essential elements */
            nav,
            aside,
            footer,
            button,
            .print:hidden,
            #sidebar,
            #app-header,
            [aria-label="Main navigation"],
            .print-hide {
              display: none !important;
            }

            /* Show the main content */
            main,
            .print-content {
              display: block !important;
              width: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              overflow: visible !important;
              max-width: 190mm !important; /* A4 width minus margins */
            }

            /* Prevent page breaks inside elements */
            .print-content * {
              page-break-inside: avoid !important;
            }

            /* Format the invoice header */
            .invoice-company-header {
              display: flex !important;
              justify-content: space-between !important;
              margin-bottom: 5mm !important;
              padding-bottom: 2mm !important;
              border-bottom: 0.5pt solid #ddd !important;
              max-height: 20mm !important;
            }

            .invoice-company-logo {
              max-width: 25mm !important;
              max-height: 15mm !important;
              object-fit: contain !important;
            }

            .invoice-company-info {
              text-align: right !important;
              font-size: 7pt !important;
              line-height: 1.1 !important;
            }

            /* Format tables */
            table {
              width: 100% !important;
              border-collapse: collapse !important;
              table-layout: fixed !important;
            }

            table th,
            table td {
              padding: 1mm 2mm !important;
              text-align: left !important;
              border-bottom: 0.5pt solid #ddd !important;
              font-size: 8pt !important;
              overflow: hidden !important;
              text-overflow: ellipsis !important;
              white-space: nowrap !important;
            }

            /* Adjust font sizes */
            h1,
            h2,
            h3,
            p,
            span,
            div {
              margin: 0 !important;
              padding: 0 !important;
            }

            h1 {
              font-size: 12pt !important;
              margin-bottom: 2mm !important;
            }

            h2 {
              font-size: 10pt !important;
              margin-bottom: 1mm !important;
            }

            p,
            span,
            div {
              font-size: 8pt !important;
              line-height: 1.2 !important;
            }

            /* Format the order items */
            .invoice-items {
              width: 100% !important;
              margin-bottom: 3mm !important;
            }

            .invoice-items th {
              background-color: #f5f5f5 !important;
              font-weight: bold !important;
              font-size: 8pt !important;
            }

            .invoice-items td {
              font-size: 8pt !important;
            }

            /* Format the order totals */
            .invoice-totals {
              width: 100% !important;
              margin-top: 3mm !important;
            }

            .invoice-totals td {
              padding: 1mm !important;
              font-size: 8pt !important;
            }

            .invoice-totals .total-row {
              font-weight: bold !important;
              border-top: 0.5pt solid #000 !important;
            }

            /* Add a thank you message at the bottom */
            .invoice-footer {
              margin-top: 5mm !important;
              text-align: center !important;
              font-size: 7pt !important;
              color: #666 !important;
              position: absolute !important;
              bottom: 5mm !important;
              width: 100% !important;
            }

            /* Compact layout for single page */
            .flex,
            .grid {
              display: block !important;
            }

            /* Remove all margins and padding */
            .m-1,
            .m-2,
            .m-3,
            .m-4,
            .m-5,
            .m-6,
            .mt-1,
            .mt-2,
            .mt-3,
            .mt-4,
            .mt-5,
            .mt-6,
            .mb-1,
            .mb-2,
            .mb-3,
            .mb-4,
            .mb-5,
            .mb-6,
            .ml-1,
            .ml-2,
            .ml-3,
            .ml-4,
            .ml-5,
            .ml-6,
            .mr-1,
            .mr-2,
            .mr-3,
            .mr-4,
            .mr-5,
            .mr-6,
            .mx-1,
            .mx-2,
            .mx-3,
            .mx-4,
            .mx-5,
            .mx-6,
            .my-1,
            .my-2,
            .my-3,
            .my-4,
            .my-5,
            .my-6,
            .p-1,
            .p-2,
            .p-3,
            .p-4,
            .p-5,
            .p-6,
            .pt-1,
            .pt-2,
            .pt-3,
            .pt-4,
            .pt-5,
            .pt-6,
            .pb-1,
            .pb-2,
            .pb-3,
            .pb-4,
            .pb-5,
            .pb-6,
            .pl-1,
            .pl-2,
            .pl-3,
            .pl-4,
            .pl-5,
            .pl-6,
            .pr-1,
            .pr-2,
            .pr-3,
            .pr-4,
            .pr-5,
            .pr-6,
            .px-1,
            .px-2,
            .px-3,
            .px-4,
            .px-5,
            .px-6,
            .py-1,
            .py-2,
            .py-3,
            .py-4,
            .py-5,
            .py-6 {
              margin: 0 !important;
              padding: 0 !important;
            }

            /* Specific spacing for print layout */
            .print-mb-1 {
              margin-bottom: 1mm !important;
            }

            .print-mb-2 {
              margin-bottom: 2mm !important;
            }

            .print-mb-5 {
              margin-bottom: 5mm !important;
            }
          }

          /* Styles for print mode class */
          body.print-mode {
            overflow: auto !important;
            position: static !important;
            height: auto !important;
            width: auto !important;
          }

          body.print-mode nav,
          body.print-mode aside,
          body.print-mode footer,
          body.print-mode header,
          body.print-mode .print-hide {
            display: none !important;
          }
        `}</style>
      )}

      {/* Hidden invoice template that will only be visible when printing - A4 optimized */}
      {order && (
        <div className="hidden print:block print-content">
          {/* Company Header - A4 optimized */}
          <div className="invoice-company-header">
            <div>
              <img
                src="/images/high-foods-logo.jpeg"
                alt="HighQ Foods"
                className="invoice-company-logo"
                style={{
                  maxWidth: "25mm",
                  maxHeight: "15mm",
                  objectFit: "contain",
                }}
              />
            </div>
            <div className="invoice-company-info" style={{ width: "100mm" }}>
              <p
                style={{
                  fontWeight: "bold",
                  fontSize: "9pt",
                  marginBottom: "1mm",
                }}
              >
                HighQ Foods
              </p>
              <p style={{ fontSize: "7pt", lineHeight: "1.1" }}>
                Door No: 2-56, CHANIKYAPURI COLONY 2ND LINE, 34 MC DIVISION,
                NEAR D MART, ELURU URBAN, ELURU - 534002
              </p>
            </div>
          </div>

          {/* Invoice Title and Order Info - A4 optimized */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "3mm",
            }}
          >
            <div
              style={{
                fontWeight: "bold",
                fontSize: "12pt",
                textTransform: "uppercase",
              }}
            >
              Invoice
            </div>
            <div style={{ textAlign: "right", fontSize: "8pt" }}>
              <p>
                <strong>Order #:</strong> {order.order_number} |{" "}
                <strong>Date:</strong> {formatDate(order.created_at)}
              </p>
            </div>
          </div>

          {/* Customer and Order Info in 2 columns - A4 optimized */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              marginBottom: "3mm",
            }}
          >
            <div style={{ width: "90mm" }}>
              <table
                style={{
                  width: "100%",
                  borderCollapse: "collapse",
                  fontSize: "8pt",
                }}
              >
                <tbody>
                  <tr>
                    <td
                      style={{
                        width: "25mm",
                        padding: "1mm 0",
                        verticalAlign: "top",
                      }}
                    >
                      <strong>Customer:</strong>
                    </td>
                    <td style={{ padding: "1mm 0" }}>{order.customer.name}</td>
                  </tr>
                  <tr>
                    <td style={{ padding: "1mm 0", verticalAlign: "top" }}>
                      <strong>Phone:</strong>
                    </td>
                    <td style={{ padding: "1mm 0" }}>{order.customer.phone}</td>
                  </tr>
                  <tr>
                    <td style={{ padding: "1mm 0", verticalAlign: "top" }}>
                      <strong>Status:</strong>
                    </td>
                    <td style={{ padding: "1mm 0" }}>{order.order_status}</td>
                  </tr>
                  <tr>
                    <td style={{ padding: "1mm 0", verticalAlign: "top" }}>
                      <strong>Payment:</strong>
                    </td>
                    <td style={{ padding: "1mm 0" }}>
                      {order.payment_status} ({order.payment_method})
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style={{ width: "90mm" }}>
              <table
                style={{
                  width: "100%",
                  borderCollapse: "collapse",
                  fontSize: "8pt",
                }}
              >
                <tbody>
                  {/* Always show customer's address if available */}
                  {order.address ? (
                    <>
                      <tr>
                        <td
                          style={{
                            width: "25mm",
                            padding: "1mm 0",
                            verticalAlign: "top",
                          }}
                        >
                          <strong>Customer Address:</strong>
                        </td>
                        <td style={{ padding: "1mm 0" }}>
                          {order.address.address}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: "1mm 0", verticalAlign: "top" }}>
                          <strong>City/ZIP:</strong>
                        </td>
                        <td style={{ padding: "1mm 0" }}>
                          {order.address.city}, {order.address.zip_code}
                        </td>
                      </tr>
                    </>
                  ) : (
                    <tr>
                      <td
                        style={{
                          width: "25mm",
                          padding: "1mm 0",
                          verticalAlign: "top",
                        }}
                      >
                        <strong>Customer Address:</strong>
                      </td>
                      <td style={{ padding: "1mm 0" }}>Not provided</td>
                    </tr>
                  )}

                  {/* Show delivery location if available */}
                  {order.location && (
                    <tr>
                      <td
                        style={{
                          width: "25mm",
                          padding: "1mm 0",
                          verticalAlign: "top",
                          paddingTop: "2mm",
                        }}
                      >
                        <strong>Delivery Location:</strong>
                      </td>
                      <td style={{ padding: "1mm 0", paddingTop: "2mm" }}>
                        {order.location.name}, {order.location.address}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Order Items - A4 optimized table */}
          <div style={{ marginBottom: "3mm" }}>
            <table
              style={{
                width: "100%",
                borderCollapse: "collapse",
                tableLayout: "fixed",
              }}
            >
              <thead>
                <tr style={{ backgroundColor: "#f5f5f5" }}>
                  <th
                    style={{
                      width: "90mm",
                      padding: "1mm 2mm",
                      fontSize: "8pt",
                      fontWeight: "bold",
                      textAlign: "left",
                      border: "0.5pt solid #ddd",
                    }}
                  >
                    Item
                  </th>
                  <th
                    style={{
                      width: "20mm",
                      padding: "1mm 2mm",
                      fontSize: "8pt",
                      fontWeight: "bold",
                      textAlign: "center",
                      border: "0.5pt solid #ddd",
                    }}
                  >
                    Qty
                  </th>
                  <th
                    style={{
                      width: "35mm",
                      padding: "1mm 2mm",
                      fontSize: "8pt",
                      fontWeight: "bold",
                      textAlign: "right",
                      border: "0.5pt solid #ddd",
                    }}
                  >
                    Price
                  </th>
                  <th
                    style={{
                      width: "35mm",
                      padding: "1mm 2mm",
                      fontSize: "8pt",
                      fontWeight: "bold",
                      textAlign: "right",
                      border: "0.5pt solid #ddd",
                    }}
                  >
                    Total
                  </th>
                </tr>
              </thead>
              <tbody>
                {orderItems.map((item) => (
                  <tr key={item.id}>
                    <td
                      style={{
                        padding: "1mm 2mm",
                        fontSize: "8pt",
                        border: "0.5pt solid #ddd",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {item.product_name}
                      {item.unit_type && item.unit_value && (
                        <span style={{ fontSize: "7pt" }}>
                          {" "}
                          (
                          {item.unit_type === "quantity"
                            ? `${item.unit_value} ${
                                item.unit_value > 1 ? "items" : "item"
                              }`
                            : `${item.unit_value}${item.unit_type}`}
                          )
                        </span>
                      )}
                    </td>
                    <td
                      style={{
                        padding: "1mm 2mm",
                        fontSize: "8pt",
                        border: "0.5pt solid #ddd",
                        textAlign: "center",
                      }}
                    >
                      {item.quantity}
                    </td>
                    <td
                      style={{
                        padding: "1mm 2mm",
                        fontSize: "8pt",
                        border: "0.5pt solid #ddd",
                        textAlign: "right",
                      }}
                    >
                      {formatCurrency(item.product_price)}
                    </td>
                    <td
                      style={{
                        padding: "1mm 2mm",
                        fontSize: "8pt",
                        border: "0.5pt solid #ddd",
                        textAlign: "right",
                      }}
                    >
                      {formatCurrency(item.total_price)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Order Summary - A4 optimized */}
          <div style={{ marginLeft: "auto", width: "90mm" }}>
            <table style={{ width: "100%", borderCollapse: "collapse" }}>
              <tbody>
                <tr>
                  <td
                    style={{
                      padding: "1mm",
                      fontSize: "8pt",
                      textAlign: "right",
                      width: "60mm",
                    }}
                  >
                    <strong>Subtotal:</strong>
                  </td>
                  <td
                    style={{
                      padding: "1mm",
                      fontSize: "8pt",
                      textAlign: "right",
                      width: "30mm",
                    }}
                  >
                    {formatCurrency(
                      order.total_amount -
                        (order.delivery_fee || 0) +
                        (order.discount_amount || 0)
                    )}
                  </td>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "1mm",
                      fontSize: "8pt",
                      textAlign: "right",
                    }}
                  >
                    <strong>Delivery Fee:</strong>
                  </td>
                  <td
                    style={{
                      padding: "1mm",
                      fontSize: "8pt",
                      textAlign: "right",
                    }}
                  >
                    {formatCurrency(order.delivery_fee || 0)}
                  </td>
                </tr>
                {order.discount_amount > 0 && (
                  <tr>
                    <td
                      style={{
                        padding: "1mm",
                        fontSize: "8pt",
                        textAlign: "right",
                      }}
                    >
                      <strong>
                        Discount{" "}
                        {order.coupon_code ? `(${order.coupon_code})` : ""}:
                      </strong>
                    </td>
                    <td
                      style={{
                        padding: "1mm",
                        fontSize: "8pt",
                        textAlign: "right",
                      }}
                    >
                      -{formatCurrency(order.discount_amount || 0)}
                    </td>
                  </tr>
                )}
                <tr>
                  <td
                    style={{
                      padding: "1mm",
                      fontSize: "9pt",
                      fontWeight: "bold",
                      textAlign: "right",
                      borderTop: "0.5pt solid #000",
                    }}
                  >
                    <strong>Total:</strong>
                  </td>
                  <td
                    style={{
                      padding: "1mm",
                      fontSize: "9pt",
                      fontWeight: "bold",
                      textAlign: "right",
                      borderTop: "0.5pt solid #000",
                    }}
                  >
                    {formatCurrency(order.total_amount || 0)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          {/* Special Instructions if any - A4 optimized */}
          {order.address && order.address.instructions && (
            <div style={{ marginTop: "3mm", fontSize: "8pt" }}>
              <p>
                <strong>Special Instructions:</strong>{" "}
                {order.address.instructions}
              </p>
            </div>
          )}

          {/* Thank You Message - A4 optimized */}
          <div
            style={{
              marginTop: "5mm",
              textAlign: "center",
              fontSize: "7pt",
              color: "#666",
              borderTop: "0.5pt solid #ddd",
              paddingTop: "2mm",
            }}
          >
            <p>
              Thank you for your order! For any questions, please contact us at
              <EMAIL>
            </p>
          </div>
        </div>
      )}
    </>
  );
}
