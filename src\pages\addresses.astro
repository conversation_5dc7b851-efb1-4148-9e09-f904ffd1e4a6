---
import MainLayout from "../layouts/MainLayout.astro";
import ManageAddresses from "../components/address/ManageAddresses.jsx";
console.log("🚀 ~ Astro.url.searchParams.get('new'):", Astro.url.searchParams.get('new'))

---

<MainLayout
  title="Manage Addresses - HighQ Foods"
  headerTitle="My Addresses"
  showHeader={true}
  showBackButton={true}
>
  <ManageAddresses client:load showNewForm={Astro.url.searchParams.get('new') === 'true'} />
</MainLayout>

<script>
  // Check authentication status on page load
  document.addEventListener("DOMContentLoaded", () => {
    // Check if user is authenticated
    if (typeof window.ApiClient !== "undefined") {
      if (!window.ApiClient.isAuthenticated()) {
        // Save current URL to redirect back after login
        const currentPath = window.location.pathname + window.location.search;
        window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
      } else {
        // We'll now pass the parameter directly as a prop instead of using events
        // Clean URL for better UX (remove the query parameter)
        // const urlParams = new URLSearchParams(window.location.search);
        // if (urlParams.get('new') === 'true') {
        //   const url = new URL(window.location);
        //   url.searchParams.delete('new');
        //   history.replaceState({}, document.title, url);
        // }
      }
    }
  });
</script>
