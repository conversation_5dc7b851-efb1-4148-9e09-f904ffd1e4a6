-- -- Create Coupons Table
-- CREATE TABLE IF NOT EXISTS coupons (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   code TEXT NOT NULL UNIQUE,
--   type TEXT NOT NULL, -- 'percent', 'flat', 'freeDelivery'
--   value DECIMAL(10, 2), -- discount amount or percent
--   description TEXT NOT NULL,
--   min_order_amount DECIMAL(10, 2) DEFAULT 0, -- minimum order amount to apply
--   max_discount DECIMAL(10, 2), -- maximum discount amount (for percent coupons)
--   is_active BOOLEAN DEFAULT 1,
--   start_date TIMESTAMP,
--   end_date TIMESTAMP,
--   usage_limit INTEGER, -- total number of times this coupon can be used
--   user_limit INTEGER DEFAULT 1, -- times a single user can use this coupon
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );

-- Insert some example coupons
INSERT INTO coupons (code, type, value, description, min_order_amount, max_discount, is_active, start_date, end_date, usage_limit)
VALUES 
-- ('WELCOME10', 'percent', 10, '10% off your order', 0, 500, 1, '2023-01-01', '2024-12-31', 1000),
-- ('FREEDEL', 'freeDelivery', 0, 'Free delivery on your order', 500, NULL, 1, '2023-01-01', '2024-12-31', 500),
('FLAT502', 'flat', 50, '₹50 off your order', 200, NULL, 1, NULL, NULL, NULL),
('FLAT100', 'flat', 100, '₹100 off your order', 500, NULL, 1, NULL, NULL, NULL),
('FLAT200', 'flat', 200, '₹200 off your order', 1000, NULL, 1, NULL, NULL, NULL),
('FLAT500', 'flat', 500, '₹500 off your order', 2000, NULL, 1, NULL, NULL, NULL),
('FLAT1000', 'flat', 1000, '₹1000 off your order', 5000, NULL, 1, NULL, NULL, NULL);
