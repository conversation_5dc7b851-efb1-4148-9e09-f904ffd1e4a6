-- -- Create Categories Table
-- DROP TABLE IF EXISTS categories;
-- DROP TABLE IF EXISTS products;
-- DROP TABLE IF EXISTS product_images;
-- DROP TABLE IF EXISTS product_tags;
-- DROP TABLE IF EXISTS nutritional_info;
-- DROP TABLE IF EXISTS promotions;
-- DROP TABLE IF EXISTS users;

-- CREATE TABLE IF NOT EXISTS categories (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   name TEXT NOT NULL,
--   icon TEXT NOT NULL,
--   color TEXT NOT NULL
-- );

-- -- Create Products Table
-- CREATE TABLE IF NOT EXISTS products (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   name TEXT NOT NULL,
--   url_slug TEXT NOT NULL UNIQUE,
--   description TEXT,
--   price DECIMAL(10, 2) NOT NULL,
--   old_price DECIMAL(10, 2),
--   image TEXT NOT NULL,
--   category_id INTEGER,
--   is_featured BOOLEAN DEFAULT 0,
--   is_new BOOLEAN DEFAULT 0,
--   is_on_sale BOOLEAN DEFAULT 0,
--   unit_type TEXT DEFAULT 'quantity', -- 'ml', 'kg', 'quantity', etc
--   unit_value DECIMAL(10, 2) DEFAULT 1, -- The value of the unit (500ml, 1kg, 1 item)
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   FOREIGN KEY (category_id) REFERENCES categories(id)
-- );

-- -- Create Product Images Table (for additional images)
-- CREATE TABLE IF NOT EXISTS product_images (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   product_id INTEGER NOT NULL,
--   image_url TEXT NOT NULL,
--   sort_order INTEGER DEFAULT 0,
--   FOREIGN KEY (product_id) REFERENCES products(id)
-- );

-- -- Create Product Tags Table
-- CREATE TABLE IF NOT EXISTS product_tags (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   product_id INTEGER NOT NULL,
--   tag TEXT NOT NULL,
--   FOREIGN KEY (product_id) REFERENCES products(id)
-- );

-- -- Create Nutritional Info Table
-- CREATE TABLE IF NOT EXISTS nutritional_info (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   product_id INTEGER NOT NULL UNIQUE,
--   calories TEXT,
--   allergens TEXT,
--   ingredients TEXT,
--   FOREIGN KEY (product_id) REFERENCES products(id)
-- );

-- -- Create Promotions Table
-- CREATE TABLE IF NOT EXISTS promotions (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   title TEXT NOT NULL,
--   description TEXT,
--   image TEXT NOT NULL,
--   url TEXT NOT NULL,
--   color TEXT NOT NULL,
--   active BOOLEAN DEFAULT 1
-- );

-- -- Create Users Table
-- CREATE TABLE IF NOT EXISTS users (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   name TEXT NOT NULL,
--   phone_number TEXT UNIQUE NOT NULL,
--   points INTEGER DEFAULT 0,
--   level TEXT DEFAULT 'Bronze'
-- );


-- -- DELETE FROM product_images;
-- -- DELETE FROM product_tags;
-- -- DELETE FROM nutritional_info;
-- -- DELETE FROM products;
-- -- DELETE FROM categories;
-- -- DELETE FROM promotions;
-- -- DELETE FROM users;

-- ALTER TABLE products ADD COLUMN unit_type TEXT DEFAULT 'quantity'; -- 'ml', 'kg', 'quantity', etc
-- ALTER TABLE products ADD COLUMN unit_value DECIMAL(10, 2) DEFAULT 1; -- The value of the unit (500ml, 1kg, 1 item)

-- ALTER TABLE products ADD COLUMN is_available BOOLEAN DEFAULT 1; -- 1 for available, 0 for not available
-- ALTER TABLE products ADD COLUMN stock_quantity INTEGER DEFAULT 0; -- Quantity in stock

-- SQLite doesn't support CURRENT_TIMESTAMP in ALTER TABLE
ALTER TABLE users ADD column created_at TIMESTAMP;
UPDATE users SET created_at = CURRENT_TIMESTAMP;