/**
 * Database utility functions for SnackSwift application
 */

// Types for our database models
export interface Category {
  id: number;
  name: string;
  icon: string;
  color: string;
}

export interface Product {
  id: number;
  name: string;
  url_slug: string;
  description?: string;
  price: number;
  old_price?: number;
  image: string;
  category_id?: number;
  is_featured: boolean;
  is_new: boolean;
  is_on_sale: boolean;
  is_available: boolean; // Whether the product is available for purchase
  rating?: number;
  reviews_count: number;
  unit_type: string; // 'ml', 'kg', 'quantity', etc.
  unit_value: number; // The value of the unit (500ml, 1kg, 1 item)
  stock_quantity?: number; // Quantity in stock
  created_at?: string;
  category?: string;
}

export interface ProductDetail extends Product {
  additionalImages?: string[];
  nutritionalInfo?: {
    calories?: string;
    allergens?: string;
    ingredients?: string;
  };
  relatedProducts?: Product[];
}

export interface Promotion {
  id: number;
  title: string;
  description?: string;
  image: string;
  url: string;
  color: string;
  active: boolean;
}

export interface User {
  id: number;
  name: string;
  email: string;
  points: number;
  level: string;
  role?: string; // 'customer', 'delivery_boy', 'admin'
  phone_number?: string;
}

// Add new interface for user addresses
export interface UserAddress {
  id: number;
  user_id: number;
  full_name: string;
  phone: string;
  address: string;
  city: string;
  zip_code: string;
  instructions?: string;
  is_default: boolean;
  created_at?: string;
}

// Order related interfaces
export interface OrderLocation {
  id: number;
  name: string;
  address: string;
  is_active: boolean;
  created_at?: string;
}

export interface Order {
  id: number;
  user_id: number;
  order_number: string;
  total_amount: number;
  delivery_fee: number;
  discount_amount: number;
  coupon_code?: string;
  payment_method: string;
  payment_status: string;
  order_status: string;
  address_id: number;
  location_id?: number;
  delivery_boy_id?: number;
  estimated_delivery?: string;
  delivered_at?: string;
  out_for_delivery_at?: string;
  cancel_reason?: string;
  created_at: string;
  updated_at: string;
  address?: UserAddress;
  location?: OrderLocation;
  items?: OrderItem[];
  delivery_boy?: User;
}

export interface DeliveryBoyLocation {
  id: number;
  user_id: number;
  location_id: number;
  is_active: boolean;
  created_at?: string;
  user?: User;
  location?: OrderLocation;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  product_name: string;
  product_price: number;
  quantity: number;
  total_price: number;
  product_image?: string;
}

export interface PaymentTransaction {
  id: number;
  order_id: number;
  payment_id: string;
  payment_method: string;
  amount: number;
  status: string;
  gateway_response?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get all categories
 */
export async function getCategories(env: Env): Promise<Category[]> {
  // Check if env and database connection exists
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty categories"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM categories ORDER BY id"
    ).all();

    return results as Category[];
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
}

/**
 * Get all products with optional filters and pagination
 */
export async function getProducts(
  env: Env,
  options: {
    categoryId?: number;
    featured?: boolean;
    onSale?: boolean;
    isNew?: boolean;
    page?: number;
    limit?: number;
    sort?: string;
  } = {}
): Promise<Product[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty products");
    return [];
  }

  const {
    categoryId,
    featured,
    onSale,
    isNew,
    page = 1,
    limit = 20,
    sort = "id",
  } = options;

  // Different approach - use separate queries for different filter combinations
  // to avoid dynamic parameter binding which can cause issues with D1

  let query: string;
  let params: any[] = [];

  // Base query without filters (just for category)
  if (!categoryId && !featured && !onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
    `;
  }
  // Query with only categoryId filter
  else if (categoryId && !featured && !onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.category_id = ?
    `;
    params.push(categoryId);
  }
  // Query with only featured filter
  else if (!categoryId && featured && !onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_featured = 1
    `;
  }
  // Query with only onSale filter
  else if (!categoryId && !featured && onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_on_sale = 1
    `;
  }
  // Query with only isNew filter
  else if (!categoryId && !featured && !onSale && isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_new = 1
    `;
  }
  // Query with categoryId + featured
  else if (categoryId && featured && !onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.category_id = ? AND p.is_featured = 1
    `;
    params.push(categoryId);
  }
  // Query with categoryId + onSale
  else if (categoryId && !featured && onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.category_id = ? AND p.is_on_sale = 1
    `;
    params.push(categoryId);
  }
  // Query with categoryId + isNew
  else if (categoryId && !featured && !onSale && isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.category_id = ? AND p.is_new = 1
    `;
    params.push(categoryId);
  }
  // Query with featured + onSale
  else if (!categoryId && featured && onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_featured = 1 AND p.is_on_sale = 1
    `;
  }
  // Query with featured + isNew
  else if (!categoryId && featured && !onSale && isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_featured = 1 AND p.is_new = 1
    `;
  }
  // Query with onSale + isNew
  else if (!categoryId && !featured && onSale && isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_on_sale = 1 AND p.is_new = 1
    `;
  }
  // Fallback query for any other combination
  else {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE 1=1
    `;

    if (categoryId) {
      query += " AND p.category_id = ?";
      params.push(categoryId);
    }
    if (featured) {
      query += " AND p.is_featured = 1";
    }
    if (onSale) {
      query += " AND p.is_on_sale = 1";
    }
    if (isNew) {
      query += " AND p.is_new = 1";
    }
  }

  // Add sorting (no parameters needed)
  switch (sort) {
    case "price-asc":
      query += " ORDER BY p.price ASC";
      break;
    case "price-desc":
      query += " ORDER BY p.price DESC";
      break;
    case "newest":
      query += " ORDER BY p.created_at DESC";
      break;
    case "popular":
    default:
      query += " ORDER BY p.rating DESC, p.reviews_count DESC";
      break;
  }

  // Add pagination with fixed parameter positions
  query += " LIMIT ? OFFSET ?";
  params.push(limit, (page - 1) * limit);

  // Prepare and execute query
  let stmt;
  try {
    stmt = env.SNACKSWIFT_DB.prepare(query);
    if (params.length > 0) {
      stmt = stmt.bind(...params);
    }

    const { results } = await stmt.all();

    // Format prices with $ sign
    return (results as any[]).map((product) => ({
      ...product,
      price: `₹ ${product.price}`,
      old_price: product.old_price ? `₹ ${product.old_price}` : undefined,
    }));
  } catch (error) {
    console.error("Database error in getProducts:", error);
    console.error("Query:", query);
    console.error("Params:", params);
    return []; // Return empty array instead of crashing
  }
}

/**
 * Get featured products
 */
export async function getFeaturedProducts(env: Env): Promise<Product[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty featured products"
    );
    return [];
  }

  return getProducts(env, { featured: true, limit: 3 });
}

/**
 * Get popular products
 */
export async function getPopularProducts(env: Env): Promise<Product[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty popular products"
    );
    return [];
  }

  return getProducts(env, { sort: "popular", limit: 4 });
}

/**
 * Get a single product by ID with all details
 */
export async function getProductById(
  env: Env,
  id: string
): Promise<ProductDetail | null> {
  // Get the base product
  const { results: productResults } = await env.SNACKSWIFT_DB.prepare(
    `SELECT p.*, c.name as category
     FROM products p
     LEFT JOIN categories c ON p.category_id = c.id
     WHERE p.id = ?`
  )
    .bind(id)
    .all();

  if (!productResults || productResults.length === 0) {
    return null;
  }

  const product = productResults[0] as any;

  // Format prices with $ sign
  product.price = `₹ ${product.price}`;
  if (product.old_price) {
    product.old_price = `₹ ${product.old_price}`;
  }

  // Get additional images
  const { results: imageResults } = await env.SNACKSWIFT_DB.prepare(
    "SELECT image_url FROM product_images WHERE product_id = ? ORDER BY sort_order"
  )
    .bind(id)
    .all();

  // Get nutritional info
  const { results: nutritionalResults } = await env.SNACKSWIFT_DB.prepare(
    "SELECT calories, allergens, ingredients FROM nutritional_info WHERE product_id = ?"
  )
    .bind(id)
    .all();

  // Get related products (simple approach - same category)
  let relatedProducts: Product[] = [];
  if (product.category_id) {
    const { results: relatedResults } = await env.SNACKSWIFT_DB.prepare(
      `SELECT p.*, c.name as category
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.category_id = ? AND p.id != ?
       ORDER BY RANDOM() LIMIT 3`
    )
      .bind(product.category_id, id)
      .all();

    relatedProducts = (relatedResults as any[]).map((product) => ({
      ...product,
      price: `₹ ${product.price}`,
      old_price: product.old_price ? `₹ ${product.old_price}` : undefined,
    }));
  }

  // Combine everything
  return {
    ...product,
    additionalImages: imageResults.map((img: any) => img.image_url),
    nutritionalInfo: nutritionalResults.length > 0 ? nutritionalResults[0] : {},
    relatedProducts,
  } as ProductDetail;
}

/**
 * Get a single product by URL slug with all details
 */
export async function getProductBySlug(
  env: Env,
  slug: string
): Promise<ProductDetail | null> {
  // Check if database connection exists
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available for getProductBySlug");
    return null;
  }

  try {
    // Get the base product
    const { results: productResults } = await env.SNACKSWIFT_DB.prepare(
      `SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.url_slug = ?`
    )
      .bind(slug)
      .all();

    if (!productResults || productResults.length === 0) {
      console.warn(`No product found with slug: ${slug}`);
      return null;
    }

    const product = productResults[0] as any;
    const id = product.id.toString();

    // Format prices with $ sign
    product.price = `₹ ${product.price}`;
    if (product.old_price) {
      product.old_price = `₹ ${product.old_price}`;
    }

    try {
      // Get additional images
      const { results: imageResults } = await env.SNACKSWIFT_DB.prepare(
        "SELECT image_url FROM product_images WHERE product_id = ? ORDER BY sort_order"
      )
        .bind(id)
        .all();

      // Get nutritional info
      const { results: nutritionalResults } = await env.SNACKSWIFT_DB.prepare(
        "SELECT calories, allergens, ingredients FROM nutritional_info WHERE product_id = ?"
      )
        .bind(id)
        .all();

      // Get related products (simple approach - same category)
      let relatedProducts: Product[] = [];
      if (product.category_id) {
        const { results: relatedResults } = await env.SNACKSWIFT_DB.prepare(
          `SELECT p.*, c.name as category
          FROM products p
          LEFT JOIN categories c ON p.category_id = c.id
          WHERE p.category_id = ? AND p.id != ?
          ORDER BY RANDOM() LIMIT 3`
        )
          .bind(product.category_id, id)
          .all();

        relatedProducts = (relatedResults as any[]).map((product) => ({
          ...product,
          price: `₹ ${product.price}`,
          old_price: product.old_price ? `₹ ${product.old_price}` : undefined,
        }));
      }

      // Combine everything
      return {
        ...product,
        additionalImages: imageResults.map((img: any) => img.image_url),
        nutritionalInfo:
          nutritionalResults.length > 0 ? nutritionalResults[0] : {},
        relatedProducts,
      } as ProductDetail;
    } catch (error) {
      // If secondary queries fail, still return the base product
      console.error(
        `Error fetching additional product details for ${slug}:`,
        error
      );
      return {
        ...product,
        additionalImages: [],
        nutritionalInfo: {},
        relatedProducts: [],
      } as ProductDetail;
    }
  } catch (error) {
    console.error(`Error in getProductBySlug for ${slug}:`, error);
    return null;
  }
}

/**
 * Get a product by its slug
 * @param env Environment with D1 database binding
 * @param slug The product slug
 * @returns The product data or null if not found
 */
// export async function getProductBySlug(env: any, slug: string) {
//   try {
//     const product = await env.DB.prepare(
//       `SELECT products.*, categories.name as category, categories.slug as category_slug
//        FROM products
//        LEFT JOIN categories ON products.category_id = categories.id
//        WHERE products.slug = ?`
//     )
//       .bind(slug)
//       .first();

//     if (!product) return null;

//     // Get additional images
//     const additionalImages = await env.DB.prepare(
//       `SELECT image_url FROM product_images WHERE product_id = ?`
//     )
//       .bind(product.id)
//       .all();

//     if (additionalImages?.results) {
//       product.additionalImages = additionalImages.results.map(img => img.image_url);
//     }

//     // Handle nutritional info
//     if (product.nutritional_info) {
//       try {
//         product.nutritionalInfo = JSON.parse(product.nutritional_info);
//       } catch (e) {
//         console.error("Error parsing nutritional info:", e);
//         product.nutritionalInfo = {};
//       }
//     }

//     // Get tags
//     const tags = await env.DB.prepare(
//       `SELECT t.name FROM product_tags pt
//        JOIN tags t ON pt.tag_id = t.id
//        WHERE pt.product_id = ?`
//     )
//       .bind(product.id)
//       .all();

//     if (tags?.results) {
//       product.tags = tags.results.map(tag => tag.name);
//     }

//     return product;
//   } catch (error) {
//     console.error("Database error in getProductBySlug:", error);
//     return null;
//   }
// }

/**
 * Get all active promotions
 */
export async function getPromotions(env: Env): Promise<Promotion[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty promotions"
    );
    return [];
  }

  const { results } = await env.SNACKSWIFT_DB.prepare(
    "SELECT * FROM promotions WHERE active = 1"
  ).all();

  return results as Promotion[];
}

// /**
//  * Get user by ID
//  */
// export async function getUserById(env: Env, id: string): Promise<User | null> {
//   const { results } = await env.SNACKSWIFT_DB.prepare(
//     "SELECT * FROM users WHERE id = ?"
//   ).bind(id).all();

//   if (!results || results.length === 0) {
//     return null;
//   }

//   return results[0] as User;
// }

/**
 * Get first user (for demo purposes)
 */
export async function getFirstUser(env: Env): Promise<User | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning null user");
    return null;
  }

  const { results } = await env.SNACKSWIFT_DB.prepare(
    "SELECT * FROM users ORDER BY id LIMIT 1"
  ).all();

  if (!results || results.length === 0) {
    return null;
  }

  return results[0] as User;
}

/**
 * Get user addresses
 */
export async function getUserAddresses(
  env: Env,
  userId: number
): Promise<UserAddress[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty addresses"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC"
    )
      .bind(userId)
      .all();

    return results as UserAddress[];
  } catch (error) {
    console.error("Error fetching user addresses:", error);
    return [];
  }
}

/**
 * Get default user address
 */
export async function getDefaultUserAddress(
  env: Env,
  userId: number
): Promise<UserAddress | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning null address");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE user_id = ? AND is_default = 1 LIMIT 1"
    )
      .bind(userId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as UserAddress;
  } catch (error) {
    console.error("Error fetching default user address:", error);
    return null;
  }
}

/**
 * Add new user address
 */
export async function addUserAddress(
  env: Env,
  address: Omit<UserAddress, "id" | "created_at">
): Promise<UserAddress | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't add address");
    return null;
  }

  try {
    // If this is set as default, unset any existing default first
    if (address.is_default) {
      await env.SNACKSWIFT_DB.prepare(
        "UPDATE user_addresses SET is_default = 0 WHERE user_id = ?"
      )
        .bind(address.user_id)
        .run();
    }

    // Insert the new address
    const result = await env.SNACKSWIFT_DB.prepare(
      `INSERT INTO user_addresses (user_id, full_name, phone, address, city, zip_code, instructions, is_default)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
    )
      .bind(
        address.user_id,
        address.full_name,
        address.phone,
        address.address,
        address.city,
        address.zip_code,
        address.instructions || "",
        address.is_default ? 1 : 0
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the newly created address with its ID
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE id = ?"
    )
      .bind(result.meta.last_row_id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as UserAddress;
  } catch (error) {
    console.error("Error adding user address:", error);
    return null;
  }
}

/**
 * Update an existing user address
 */
export async function updateUserAddress(
  env: Env,
  address: UserAddress
): Promise<UserAddress | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update address");
    return null;
  }

  try {
    // Verify the address belongs to the user first (security check)
    const { results: existingAddress } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE id = ? AND user_id = ?"
    )
      .bind(address.id, address.user_id)
      .all();

    if (!existingAddress || existingAddress.length === 0) {
      console.warn("Address not found or doesn't belong to the user");
      return null;
    }

    // If this is set as default, unset any existing default first
    if (address.is_default) {
      await env.SNACKSWIFT_DB.prepare(
        "UPDATE user_addresses SET is_default = 0 WHERE user_id = ?"
      )
        .bind(address.user_id)
        .run();
    }

    // Update the address
    const result = await env.SNACKSWIFT_DB.prepare(
      `UPDATE user_addresses
       SET full_name = ?, phone = ?, address = ?, city = ?, zip_code = ?,
           instructions = ?, is_default = ?
       WHERE id = ? AND user_id = ?`
    )
      .bind(
        address.full_name,
        address.phone,
        address.address,
        address.city,
        address.zip_code,
        address.instructions || "",
        address.is_default ? 1 : 0,
        address.id,
        address.user_id
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the updated address
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE id = ?"
    )
      .bind(address.id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as UserAddress;
  } catch (error) {
    console.error("Error updating user address:", error);
    return null;
  }
}

/**
 * Delete a user address
 */
export async function deleteUserAddress(
  env: Env,
  addressId: number,
  userId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't delete address");
    return false;
  }

  try {
    // Verify the address belongs to the user first (security check)
    const { results: existingAddress } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE id = ? AND user_id = ?"
    )
      .bind(addressId, userId)
      .all();

    if (!existingAddress || existingAddress.length === 0) {
      console.warn("Address not found or doesn't belong to the user");
      return false;
    }

    // Delete the address
    const result = await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM user_addresses WHERE id = ? AND user_id = ?"
    )
      .bind(addressId, userId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error deleting user address:", error);
    return false;
  }
}

/**
 * Find user by phone number
 */
export async function findUserByPhone(
  env: Env,
  phoneNumber: string
): Promise<User | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE phone_number = ?"
    )
      .bind(phoneNumber)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as User;
  } catch (error) {
    console.error("Error finding user by phone:", error);
    return null;
  }
}

/**
 * Generate a new OTP code for the given phone number
 */
export async function generateOTP(
  env: Env,
  phoneNumber: string
): Promise<string | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available");
    return null;
  }

  try {
    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    // OTP expires in 10 minutes
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 10 * 60 * 1000); // 10 minutes

    // First invalidate any existing OTPs for this phone number
    await env.SNACKSWIFT_DB.prepare(
      "UPDATE otp_verifications SET is_used = 1 WHERE phone_number = ? AND is_used = 0"
    )
      .bind(phoneNumber)
      .run();

    // Insert new OTP
    const result = await env.SNACKSWIFT_DB.prepare(
      `INSERT INTO otp_verifications (phone_number, otp_code, expires_at)
       VALUES (?, ?, ?)`
    )
      .bind(phoneNumber, otp, expiresAt.toISOString())
      .run();

    if (!result || !result.success) {
      return null;
    }

    return otp;
  } catch (error) {
    console.error("Error generating OTP:", error);
    return null;
  }
}

/**
 * Verify an OTP code
 */
export async function verifyOTP(
  env: Env,
  phoneNumber: string,
  otpCode: string
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available");
    return false;
  }

  try {
    // Get the latest valid OTP for this phone number
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT * FROM otp_verifications
       WHERE phone_number = ? AND otp_code = ? AND is_used = 0 AND expires_at > datetime('now')
       ORDER BY created_at DESC LIMIT 1`
    )
      .bind(phoneNumber, otpCode)
      .all();

    if (!results || results.length === 0) {
      // Increment attempts for invalid OTPs
      await env.SNACKSWIFT_DB.prepare(
        `UPDATE otp_verifications SET attempts = attempts + 1
         WHERE phone_number = ? AND is_used = 0`
      )
        .bind(phoneNumber)
        .run();

      return false;
    }

    // Mark OTP as used
    await env.SNACKSWIFT_DB.prepare(
      "UPDATE otp_verifications SET is_used = 1 WHERE id = ?"
    )
      .bind(results[0].id)
      .run();

    return true;
  } catch (error) {
    console.error("Error verifying OTP:", error);
    return false;
  }
}

/**
 * Create or update a user after OTP verification
 */
export async function createOrUpdateUser(
  env: Env,
  userData: {
    phone_number: string;
    name?: string;
    is_verified: boolean;
  }
): Promise<User | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available");
    return null;
  }

  try {
    // Check if user exists
    const existingUser = await findUserByPhone(env, userData.phone_number);

    if (existingUser) {
      // Update existing user
      await env.SNACKSWIFT_DB.prepare(
        "UPDATE users SET is_verified = ? WHERE id = ?"
      )
        .bind(userData.is_verified ? 1 : 0, existingUser.id)
        .run();

      return {
        ...existingUser,
        is_verified: userData.is_verified,
      };
    } else {
      // Create new user
      const name = userData.name || `User${Math.floor(Math.random() * 10000)}`;

      const result = await env.SNACKSWIFT_DB.prepare(
        `INSERT INTO users (name, phone_number, is_verified, points, level)
         VALUES (?, ?, ?, ?, ?)`
      )
        .bind(
          name,
          userData.phone_number,
          userData.is_verified ? 1 : 0,
          0, // Initial points
          "Bronze" // Initial level
        )
        .run();

      if (!result || !result.success) {
        return null;
      }

      // Get the newly created user
      const { results } = await env.SNACKSWIFT_DB.prepare(
        "SELECT * FROM users WHERE id = ?"
      )
        .bind(result.meta.last_row_id)
        .all();

      if (!results || results.length === 0) {
        return null;
      }

      return results[0] as User;
    }
  } catch (error) {
    console.error("Error creating/updating user:", error);
    return null;
  }
}

/**
 * Get user by ID
 */
export async function getUserById(
  env: Env,
  userId: number
): Promise<any | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get user");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE id = ?"
    )
      .bind(userId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0];
  } catch (error) {
    console.error("Error getting user by ID:", error);
    return null;
  }
}

/**
 * Update user profile
 */
export async function updateUser(env: Env, user: any): Promise<any | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update user");
    return null;
  }

  try {
    // Update only name and email in the database
    const result = await env.SNACKSWIFT_DB.prepare(
      `UPDATE users
       SET name = ?, email = ?
       WHERE id = ?`
    )
      .bind(user.name, user.email, user.id)
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the updated user
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT id, name, email FROM users WHERE id = ?"
    )
      .bind(user.id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0];
  } catch (error) {
    console.error("Error updating user:", error);
    return null;
  }
}

// Coupon interface
export interface Coupon {
  id: number;
  code: string;
  type: string; // 'percent', 'flat', 'freeDelivery'
  value: number;
  description: string;
  min_order_amount: number;
  max_discount?: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
  usage_limit?: number;
  user_limit: number;
  created_at?: string;
}

/**
 * Get all coupons (admin)
 */
export async function getAllCoupons(env: Env): Promise<Coupon[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty coupons");
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM coupons ORDER BY created_at DESC"
    ).all();

    return results as Coupon[];
  } catch (error) {
    console.error("Error fetching all coupons:", error);
    return [];
  }
}

/**
 * Get coupon by ID (admin)
 */
export async function getCouponById(
  env: Env,
  id: string | number
): Promise<Coupon | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get coupon");
    return null;
  }

  try {
    // Check if id is a number or a coupon code
    const isCode = typeof id === "string" && isNaN(parseInt(id));

    const query = isCode
      ? "SELECT * FROM coupons WHERE code = ?"
      : "SELECT * FROM coupons WHERE id = ?";

    const { results } = await env.SNACKSWIFT_DB.prepare(query)
      .bind(isCode ? id.toUpperCase() : id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as Coupon;
  } catch (error) {
    console.error("Error getting coupon by ID:", error);
    return null;
  }
}

/**
 * Create a new coupon (admin)
 */
export async function createCoupon(
  env: Env,
  coupon: Omit<Coupon, "id" | "created_at">
): Promise<Coupon | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't create coupon");
    return null;
  }

  try {
    // Ensure the coupons table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS coupons (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        value DECIMAL(10, 2),
        description TEXT NOT NULL,
        min_order_amount DECIMAL(10, 2) DEFAULT 0,
        max_discount DECIMAL(10, 2),
        is_active BOOLEAN DEFAULT 1,
        start_date TIMESTAMP,
        end_date TIMESTAMP,
        usage_limit INTEGER,
        user_limit INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    // Insert the new coupon
    const result = await env.SNACKSWIFT_DB.prepare(
      `
      INSERT INTO coupons (
        code, type, value, description, min_order_amount, max_discount,
        is_active, start_date, end_date, usage_limit, user_limit
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    )
      .bind(
        coupon.code.toUpperCase(),
        coupon.type,
        coupon.value,
        coupon.description,
        coupon.min_order_amount,
        coupon.max_discount,
        coupon.is_active ? 1 : 0,
        coupon.start_date,
        coupon.end_date,
        coupon.usage_limit,
        coupon.user_limit
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the newly created coupon
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM coupons WHERE id = ?"
    )
      .bind(result.meta.last_row_id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as Coupon;
  } catch (error) {
    console.error("Error creating coupon:", error);
    return null;
  }
}

/**
 * Update an existing coupon (admin)
 */
export async function updateCoupon(
  env: Env,
  coupon: Coupon
): Promise<Coupon | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update coupon");
    return null;
  }

  try {
    // Update the coupon
    const result = await env.SNACKSWIFT_DB.prepare(
      `
      UPDATE coupons
      SET type = ?, value = ?, description = ?, min_order_amount = ?,
          max_discount = ?, is_active = ?, start_date = ?, end_date = ?,
          usage_limit = ?, user_limit = ?
      WHERE id = ?
    `
    )
      .bind(
        coupon.type,
        coupon.value,
        coupon.description,
        coupon.min_order_amount,
        coupon.max_discount,
        coupon.is_active ? 1 : 0,
        coupon.start_date,
        coupon.end_date,
        coupon.usage_limit,
        coupon.user_limit,
        coupon.id
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the updated coupon
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM coupons WHERE id = ?"
    )
      .bind(coupon.id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as Coupon;
  } catch (error) {
    console.error("Error updating coupon:", error);
    return null;
  }
}

/**
 * Delete a coupon (admin)
 */
export async function deleteCoupon(env: Env, id: number): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't delete coupon");
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM coupons WHERE id = ?"
    )
      .bind(id)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error deleting coupon:", error);
    return false;
  }
}

// Delivery Fee Settings interface
export interface DeliveryFeeSettings {
  id?: number;
  base_fee: number;
  free_delivery_threshold: number;
  is_enabled: boolean;
  updated_at?: string;
}

// Payment Method Settings interface
export interface PaymentMethodSettings {
  id?: number;
  online_payment_enabled: boolean;
  cash_on_delivery_enabled: boolean;
  updated_at?: string;
}

// Location Delivery Fee interface
export interface LocationDeliveryFee {
  id?: number;
  location_id: number;
  location_name?: string;
  fee: number;
  free_delivery_threshold: number;
  updated_at?: string;
}

/**
 * Get delivery fee settings
 */
export async function getDeliveryFeeSettings(
  env: Env
): Promise<DeliveryFeeSettings> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning default delivery fee settings"
    );
    return {
      base_fee: 2.99,
      free_delivery_threshold: 0,
      is_enabled: true,
    };
  }

  try {
    // Ensure the delivery_fee_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS delivery_fee_settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        base_fee DECIMAL(10, 2) NOT NULL DEFAULT 2.99,
        free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
        is_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    // Check if settings exist
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM delivery_fee_settings WHERE id = 1"
    ).all();

    if (!results || results.length === 0) {
      // Insert default settings
      await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO delivery_fee_settings (id, base_fee, free_delivery_threshold, is_enabled)
        VALUES (1, 2.99, 0, 1)
      `
      ).run();

      return {
        base_fee: 2.99,
        free_delivery_threshold: 0,
        is_enabled: true,
      };
    }

    return results[0] as DeliveryFeeSettings;
  } catch (error) {
    console.error("Error getting delivery fee settings:", error);
    return {
      base_fee: 2.99,
      free_delivery_threshold: 0,
      is_enabled: true,
    };
  }
}

/**
 * Get payment method settings
 */
export async function getPaymentMethodSettings(
  env: Env
): Promise<PaymentMethodSettings> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning default payment method settings"
    );
    return {
      online_payment_enabled: true,
      cash_on_delivery_enabled: true,
    };
  }

  try {
    // Ensure the payment_method_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS payment_method_settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        online_payment_enabled BOOLEAN DEFAULT 1,
        cash_on_delivery_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    // Check if settings exist
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM payment_method_settings WHERE id = 1"
    ).all();

    if (!results || results.length === 0) {
      // Insert default settings
      await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO payment_method_settings (id, online_payment_enabled, cash_on_delivery_enabled)
        VALUES (1, 1, 1)
      `
      ).run();

      return {
        online_payment_enabled: true,
        cash_on_delivery_enabled: true,
      };
    }

    return {
      online_payment_enabled: results[0].online_payment_enabled === 1,
      cash_on_delivery_enabled: results[0].cash_on_delivery_enabled === 1,
      updated_at: results[0].updated_at,
    };
  } catch (error) {
    console.error("Error getting payment method settings:", error);
    return {
      online_payment_enabled: true,
      cash_on_delivery_enabled: true,
    };
  }
}

/**
 * Update delivery fee settings
 */
export async function updateDeliveryFeeSettings(
  env: Env,
  settings: DeliveryFeeSettings
): Promise<DeliveryFeeSettings | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update delivery fee settings"
    );
    return null;
  }

  try {
    // Ensure the delivery_fee_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS delivery_fee_settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        base_fee DECIMAL(10, 2) NOT NULL DEFAULT 2.99,
        free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
        is_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    const now = new Date().toISOString();

    // Check if settings exist
    const { results: existingSettings } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM delivery_fee_settings WHERE id = 1"
    ).all();

    let result;

    if (!existingSettings || existingSettings.length === 0) {
      // Insert settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO delivery_fee_settings (id, base_fee, free_delivery_threshold, is_enabled, updated_at)
        VALUES (1, ?, ?, ?, ?)
      `
      )
        .bind(
          settings.base_fee,
          settings.free_delivery_threshold,
          settings.is_enabled ? 1 : 0,
          now
        )
        .run();
    } else {
      // Update settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE delivery_fee_settings
        SET base_fee = ?, free_delivery_threshold = ?, is_enabled = ?, updated_at = ?
        WHERE id = 1
      `
      )
        .bind(
          settings.base_fee,
          settings.free_delivery_threshold,
          settings.is_enabled ? 1 : 0,
          now
        )
        .run();
    }

    if (!result || !result.success) {
      return null;
    }

    // Return the updated settings
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM delivery_fee_settings WHERE id = 1"
    ).all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as DeliveryFeeSettings;
  } catch (error) {
    console.error("Error updating delivery fee settings:", error);
    return null;
  }
}

/**
 * Update payment method settings
 */
export async function updatePaymentMethodSettings(
  env: Env,
  settings: PaymentMethodSettings
): Promise<PaymentMethodSettings | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update payment method settings"
    );
    return null;
  }

  try {
    // Ensure the payment_method_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS payment_method_settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        online_payment_enabled BOOLEAN DEFAULT 1,
        cash_on_delivery_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    const now = new Date().toISOString();

    // Check if settings exist
    const { results: existingSettings } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM payment_method_settings WHERE id = 1"
    ).all();

    let result;

    if (!existingSettings || existingSettings.length === 0) {
      // Insert settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO payment_method_settings (id, online_payment_enabled, cash_on_delivery_enabled, updated_at)
        VALUES (1, ?, ?, ?)
      `
      )
        .bind(
          settings.online_payment_enabled ? 1 : 0,
          settings.cash_on_delivery_enabled ? 1 : 0,
          now
        )
        .run();
    } else {
      // Update settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE payment_method_settings
        SET online_payment_enabled = ?, cash_on_delivery_enabled = ?, updated_at = ?
        WHERE id = 1
      `
      )
        .bind(
          settings.online_payment_enabled ? 1 : 0,
          settings.cash_on_delivery_enabled ? 1 : 0,
          now
        )
        .run();
    }

    if (!result || !result.success) {
      return null;
    }

    // Get updated settings
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM payment_method_settings WHERE id = 1"
    ).all();

    if (!results || results.length === 0) {
      return null;
    }

    return {
      online_payment_enabled: results[0].online_payment_enabled === 1,
      cash_on_delivery_enabled: results[0].cash_on_delivery_enabled === 1,
      updated_at: results[0].updated_at as string,
    };
  } catch (error) {
    console.error("Error updating payment method settings:", error);
    return null;
  }
}

/**
 * Get location-specific delivery fees
 */
export async function getLocationDeliveryFees(
  env: Env
): Promise<LocationDeliveryFee[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty location delivery fees"
    );
    return [];
  }

  try {
    // Ensure the location_delivery_fees table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS location_delivery_fees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        location_id INTEGER NOT NULL UNIQUE,
        fee DECIMAL(10, 2) NOT NULL,
        free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (location_id) REFERENCES order_locations(id) ON DELETE CASCADE
      )
    `
    ).run();

    // Get all location fees with location names
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT ldf.*, ol.name as location_name
      FROM location_delivery_fees ldf
      JOIN order_locations ol ON ldf.location_id = ol.id
      ORDER BY ol.name
    `
    ).all();

    return (results || []) as LocationDeliveryFee[];
  } catch (error) {
    console.error("Error getting location delivery fees:", error);
    return [];
  }
}

/**
 * Get location-specific delivery fee by location ID
 */
export async function getLocationDeliveryFeeById(
  env: Env,
  locationId: number
): Promise<LocationDeliveryFee | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't get location delivery fee"
    );
    return null;
  }

  try {
    // Ensure the location_delivery_fees table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS location_delivery_fees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        location_id INTEGER NOT NULL UNIQUE,
        fee DECIMAL(10, 2) NOT NULL,
        free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (location_id) REFERENCES order_locations(id) ON DELETE CASCADE
      )
    `
    ).run();

    // Get location fee with location name
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT ldf.*, ol.name as location_name
      FROM location_delivery_fees ldf
      JOIN order_locations ol ON ldf.location_id = ol.id
      WHERE ldf.location_id = ?
    `
    )
      .bind(locationId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as LocationDeliveryFee;
  } catch (error) {
    console.error("Error getting location delivery fee by ID:", error);
    return null;
  }
}

/**
 * Update location-specific delivery fee
 */
export async function updateLocationDeliveryFee(
  env: Env,
  locationFee: Omit<LocationDeliveryFee, "id" | "updated_at" | "location_name">
): Promise<LocationDeliveryFee | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update location delivery fee"
    );
    return null;
  }

  try {
    // Ensure the location_delivery_fees table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS location_delivery_fees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        location_id INTEGER NOT NULL UNIQUE,
        fee DECIMAL(10, 2) NOT NULL,
        free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (location_id) REFERENCES order_locations(id) ON DELETE CASCADE
      )
    `
    ).run();

    const now = new Date().toISOString();

    // Check if location fee exists
    const { results: existingFees } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM location_delivery_fees WHERE location_id = ?"
    )
      .bind(locationFee.location_id)
      .all();

    let result;

    if (!existingFees || existingFees.length === 0) {
      // Insert new location fee
      result = await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO location_delivery_fees (location_id, fee, free_delivery_threshold, updated_at)
        VALUES (?, ?, ?, ?)
      `
      )
        .bind(
          locationFee.location_id,
          locationFee.fee,
          locationFee.free_delivery_threshold,
          now
        )
        .run();
    } else {
      // Update existing location fee
      result = await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE location_delivery_fees
        SET fee = ?, free_delivery_threshold = ?, updated_at = ?
        WHERE location_id = ?
      `
      )
        .bind(
          locationFee.fee,
          locationFee.free_delivery_threshold,
          now,
          locationFee.location_id
        )
        .run();
    }

    if (!result || !result.success) {
      return null;
    }

    // Get the location name
    const { results: locationResults } = await env.SNACKSWIFT_DB.prepare(
      "SELECT name FROM order_locations WHERE id = ?"
    )
      .bind(locationFee.location_id)
      .all();

    const locationName =
      locationResults && locationResults.length > 0
        ? locationResults[0].name
        : "Unknown Location";

    // Return the updated location fee
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM location_delivery_fees WHERE location_id = ?"
    )
      .bind(locationFee.location_id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return {
      ...results[0],
      location_name: locationName,
    } as LocationDeliveryFee;
  } catch (error) {
    console.error("Error updating location delivery fee:", error);
    return null;
  }
}

/**
 * Delete a location-specific delivery fee
 */
export async function deleteLocationDeliveryFee(
  env: Env,
  locationId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't delete location delivery fee"
    );
    return false;
  }

  try {
    console.log(
      `DB Function: Deleting location fee for locationId: ${locationId}`
    );

    // Ensure the location_delivery_fees table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS location_delivery_fees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        location_id INTEGER NOT NULL UNIQUE,
        fee DECIMAL(10, 2) NOT NULL,
        free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (location_id) REFERENCES order_locations(id) ON DELETE CASCADE
      )
    `
    ).run();

    // Delete the location fee
    const result = await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM location_delivery_fees WHERE location_id = ?"
    )
      .bind(locationId)
      .run();

    console.log(`DB Function: Delete result:`, result);

    if (!result || !result.success) {
      console.error(
        `DB Function: Failed to delete location fee for locationId: ${locationId}`
      );
      return false;
    }

    // Check if any rows were affected
    if (result.meta && result.meta.changes === 0) {
      console.warn(
        `DB Function: No location fee found for locationId: ${locationId}`
      );
      return false;
    }

    console.log(
      `DB Function: Successfully deleted location fee for locationId: ${locationId}`
    );
    return true;
  } catch (error) {
    console.error(
      `DB Function: Error deleting location delivery fee for locationId: ${locationId}:`,
      error
    );
    return false;
  }
}

/**
 * Get coupon by code
 */
export async function getCouponByCode(
  env: Env,
  code: string
): Promise<any | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get coupon");
    return null;
  }

  try {
    const currentDate = new Date().toISOString();

    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM coupons
      WHERE code = ?
      AND is_active = 1
      AND (start_date IS NULL OR start_date <= ?)
      AND (end_date IS NULL OR end_date >= ?)
      AND (usage_limit IS NULL OR usage_limit > 0)
    `
    )
      .bind(code.toUpperCase(), currentDate, currentDate)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0];
  } catch (error) {
    console.error("Error getting coupon:", error);
    return null;
  }
}

/**
 * Track coupon usage
 */
export async function trackCouponUsage(
  env: Env,
  code: string,
  userId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't track coupon usage");
    return false;
  }

  try {
    // First decrement the global usage limit
    const couponResult = await env.SNACKSWIFT_DB.prepare(
      `
      UPDATE coupons
      SET usage_limit = usage_limit - 1
      WHERE code = ? AND usage_limit > 0
    `
    )
      .bind(code.toUpperCase())
      .run();

    // Then track this user's usage in a separate table (create if not exists)
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS coupon_usages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        coupon_code TEXT NOT NULL,
        user_id INTEGER NOT NULL,
        used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(coupon_code, user_id)
      )
    `
    ).run();

    // Insert the usage (or update if exists)
    const userUsageResult = await env.SNACKSWIFT_DB.prepare(
      `
      INSERT INTO coupon_usages (coupon_code, user_id)
      VALUES (?, ?)
      ON CONFLICT(coupon_code, user_id) DO UPDATE SET used_at = CURRENT_TIMESTAMP
    `
    )
      .bind(code.toUpperCase(), userId)
      .run();

    return (
      couponResult &&
      couponResult.success &&
      userUsageResult &&
      userUsageResult.success
    );
  } catch (error) {
    console.error("Error tracking coupon usage:", error);
    return false;
  }
}

/**
 * Get user coupon usage count
 */
export async function getUserCouponUsage(
  env: Env,
  code: string,
  userId: number
): Promise<number> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't get user coupon usage"
    );
    return 0;
  }

  try {
    // Check if the coupon_usages table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS coupon_usages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        coupon_code TEXT NOT NULL,
        user_id INTEGER NOT NULL,
        used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).all();

    // Get usage count
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT COUNT(*) as usage_count
      FROM coupon_usages
      WHERE coupon_code = ? AND user_id = ?
    `
    )
      .bind(code.toUpperCase(), userId)
      .all();

    if (!results || results.length === 0) {
      return 0;
    }

    return results[0].usage_count;
  } catch (error) {
    console.error("Error getting user coupon usage:", error);
    return 0;
  }
}

/**
 * Get filtered products
 */
export async function getFilteredProducts(
  env: any,
  filters: {
    page: number;
    limit: number;
    category?: number | string;
    sort: string;
    search: string;
    minPrice: number;
    maxPrice: number;
    tags: string[];
    exactMatch?: boolean;
    admin?: boolean;
  }
) {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty products");
    return {
      products: [],
      totalCount: 0,
      currentPage: filters.page,
      hasMore: false,
    };
  }

  const offset = (filters.page - 1) * filters.limit;
  let query =
    "SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE 1=1 ";
  const params: any[] = [];

  if(!filters.admin) {
    query += " AND is_available IN (1,true)";
  }
  // Apply category filter - now supports both numeric and string IDs
  if (filters.category) {
    if (
      typeof filters.category === "string" &&
      !isNaN(parseInt(filters.category))
    ) {
      query += " AND p.category_id = ?";
      params.push(parseInt(filters.category));
    } else if (typeof filters.category === "number") {
      query += " AND p.category_id = ?";
      params.push(filters.category);
    }
  }

  // Apply price range filter
  query += " AND p.price BETWEEN ? AND ?";
  params.push(filters.minPrice, filters.maxPrice);

  // Apply search filter with improved handling
  if (filters.search && filters.search.trim()) {
    const searchTerm = filters.search.trim();

    if (filters.exactMatch) {
      // Try to match by ID or exact name
      query += " AND (p.id = ? OR p.name = ?)";
      params.push(searchTerm, searchTerm);
    } else {
      // Use full-text search when available, or fallback to LIKE
      query += " AND (p.name LIKE ? OR p.description LIKE ?)";
      const likeSearchTerm = `%${searchTerm}%`;
      params.push(likeSearchTerm, likeSearchTerm);
    }
  }

  // Apply tags filter with improved handling of common tag formats
  if (filters.tags && filters.tags.length > 0) {
    const validTags = filters.tags.filter((tag) => tag); // Filter out empty values

    if (validTags.length > 0) {
      query += " AND (";
      validTags.forEach((tag, index) => {
        if (index > 0) query += " OR ";

        // Normalize tag names to handle variations
        const normalizedTag = tag.toLowerCase().replace(/-/g, "_");

        switch (normalizedTag) {
          case "new_arrivals":
          case "new":
            query += "p.is_new = 1";
            break;
          case "on_sale":
          case "sale":
            query += "p.is_on_sale = 1";
            break;
          case "bestseller":
          case "popular":
            query += "(p.rating >= 4 OR p.reviews_count >= 10)";
            break;
          case "featured":
            query += "p.is_featured = 1";
            break;
          default:
            // In a real-world scenario, we might have a dedicated tags table
            // and would join with it here for custom tags
            query += `p.name LIKE '%${tag}%'`;
            break;
        }
      });
      query += ")";
    }
  }

  try {
    // First, count total records for pagination
    const countQuery = query.replace(
      "p.*, c.name as category_name",
      "COUNT(*) as total"
    );
    const countStmt = env.SNACKSWIFT_DB.prepare(countQuery);
    const boundCountStmt =
      params.length > 0 ? countStmt.bind(...params) : countStmt;
    const countResult = await boundCountStmt.all();
    const totalCount =
      countResult.results && countResult.results.length > 0
        ? countResult.results[0].total
        : 0;

    // Apply sorting with improved handling
    switch (filters.sort) {
      case "price-asc":
        query += " ORDER BY p.is_featured DESC,p.price ASC";
        break;
      case "price-desc":
        query += " ORDER BY p.is_featured DESC,p.price DESC";
        break;
      case "newest":
        query += " ORDER BY p.is_featured DESC,p.created_at DESC, p.id DESC";
        break;
      case "bestseller":
        query += " ORDER BY p.is_featured DESC,p.rating DESC, p.reviews_count DESC";
        break;
      default: // Default to featured items first
        query += " ORDER BY p.is_featured DESC, p.rating DESC, p.id DESC";
        break;
    }

    // Add pagination
    query += " LIMIT ? OFFSET ?";
    params.push(filters.limit, offset);

    // Get paginated products
    const stmt = env.SNACKSWIFT_DB.prepare(query);
    const boundStmt = params.length > 0 ? stmt.bind(...params) : stmt;
    const result = await boundStmt.all();

    // Format products and ensure consistent property names
    const products = result.results
      ? result.results.map((product) => ({
          ...product,
          id: product.id,
          name: product.name,
          price: parseFloat(product.price || 0),
          old_price: product.old_price ? parseFloat(product.old_price) : null,
          image_url: product.image, // Ensure consistent property name
          category_name: product.category_name,
          is_new: !!product.is_new,
          is_on_sale: !!product.is_on_sale,
          is_featured: !!product.is_featured,
          url_slug: product.url_slug || `product-${product.id}`,
          rating: product.rating || null,
          stock: product.stock_quantity || 0, // Include stock quantity
        }))
      : [];

    const totalPages = Math.ceil(totalCount / filters.limit) || 1;
    const hasMore = filters.page < totalPages;

    // Return comprehensive response object
    return {
      products,
      totalCount,
      currentPage: filters.page,
      totalPages,
      hasMore,
      pageSize: filters.limit,
    };
  } catch (error) {
    console.error("Database error in getFilteredProducts:", error);
    console.error("Query:", query);
    console.error("Params:", params);

    // Return safe fallback
    return {
      products: [],
      totalCount: 0,
      currentPage: filters.page,
      totalPages: 0,
      hasMore: false,
      error: "Database error occurred",
    };
  }
}

/**
 * Generate a unique order number
 */
export async function generateOrderNumber(env: Env): Promise<string> {
  const prefix = "OD";
  const timestamp = new Date().getTime().toString().slice(-6);
  const random = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, "0");
  return `${prefix}${timestamp}${random}`;
}

/**
 * Create a new order
 */
export async function createOrder(
  env: Env,
  orderData: {
    user_id: number;
    total_amount: number;
    delivery_fee: number;
    discount_amount: number;
    coupon_code?: string;
    payment_method: string;
    address_id: number;
    location_id?: number;
    items: Array<{
      product_id: number;
      product_name: string;
      product_price: number;
      quantity: number;
      total_price: number;
    }>;
  }
): Promise<{ order?: Order; error?: string }> {
  if (!env || !env.SNACKSWIFT_DB) {
    return { error: "Database connection not available" };
  }

  try {
    const db = env.SNACKSWIFT_DB;

    // Generate an order number
    const orderNumber = await generateOrderNumber(env);

    // Calculate estimated delivery time (30-45 minutes from now)
    const now = new Date();
    const estimatedDeliveryMinutes = 30 + Math.floor(Math.random() * 15);
    const estimatedDeliveryTime = `${estimatedDeliveryMinutes} minutes`;

    // Use the JavaScript transaction API instead of SQL transactions
    const orderId = await db
      .prepare(
        `
      INSERT INTO orders (
        user_id, order_number, total_amount, delivery_fee,
        discount_amount, coupon_code, payment_method,
        payment_status, order_status, address_id, location_id, estimated_delivery
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
      )
      .bind(
        orderData.user_id,
        orderNumber,
        orderData.total_amount,
        orderData.delivery_fee,
        orderData.discount_amount,
        orderData.coupon_code || null,
        orderData.payment_method,
        orderData.payment_method === "cash" ? "pending" : "pending",
        "placed",
        orderData.address_id,
        orderData.location_id || null,
        estimatedDeliveryTime
      )
      .run()
      .then((result) => {
        if (!result.success) {
          throw new Error("Failed to create order");
        }
        return result.meta.last_row_id;
      });

    // Insert order items
    for (const item of orderData.items) {
      const itemResult = await db
        .prepare(
          `
        INSERT INTO order_items (
          order_id, product_id, product_name, product_price,
          quantity, total_price
        ) VALUES (?, ?, ?, ?, ?, ?)
      `
        )
        .bind(
          orderId,
          item.product_id,
          item.product_name,
          item.product_price,
          item.quantity,
          item.total_price
        )
        .run();

      if (!itemResult.success) {
        // If any item insert fails, we should handle this situation
        // Since we don't have automatic transaction rollback, we would need to
        // manually delete the order we just created
        await db.prepare("DELETE FROM orders WHERE id = ?").bind(orderId).run();
        return { error: "Failed to create order items" };
      }
    }

    // Track coupon usage if coupon code is provided
    if (orderData.coupon_code) {
      await trackCouponUsage(env, orderData.coupon_code, orderData.user_id);
    }

    // Return the newly created order
    const { results } = await db
      .prepare(
        `
      SELECT * FROM orders WHERE id = ?
    `
      )
      .bind(orderId)
      .all();

    if (!results || results.length === 0) {
      return { error: "Order created but not found" };
    }

    // Get order items
    const { results: itemResults } = await db
      .prepare(
        `
      SELECT * FROM order_items WHERE order_id = ?
    `
      )
      .bind(orderId)
      .all();

    // Get order address
    const { results: addressResults } = await db
      .prepare(
        `
      SELECT * FROM user_addresses WHERE id = ?
    `
      )
      .bind(orderData.address_id)
      .all();

    const order = {
      ...results[0],
      items: itemResults || [],
      address:
        addressResults && addressResults.length > 0 ? addressResults[0] : null,
    } as Order;

    return { order };
  } catch (error) {
    console.error("Error creating order:", error);
    return {
      error: error instanceof Error ? error.message : "Error creating order",
    };
  }
}

/**
 * Get order by ID
 */
export async function getOrderById(
  env: Env,
  orderId: number,
  userId: number
): Promise<Order | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get order");
    return null;
  }

  try {
    // Security check: ensure order belongs to user
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM orders WHERE id = ? AND user_id = ?
    `
    )
      .bind(orderId, userId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    const order = results[0] as Order;

    // Get order items
    const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT oi.*, p.image as product_image
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = ?
    `
    )
      .bind(orderId)
      .all();

    // Get order address
    const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM user_addresses WHERE id = ?
    `
    )
      .bind(order.address_id)
      .all();

    // Get order location if available
    let locationResult = null;
    if (order.location_id) {
      const { results: locationResults } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT * FROM order_locations WHERE id = ?
        `
      )
        .bind(order.location_id)
        .all();

      if (locationResults && locationResults.length > 0) {
        locationResult = locationResults[0];
      }
    }

    return {
      ...order,
      items: itemResults || [],
      address:
        addressResults && addressResults.length > 0 ? addressResults[0] : null,
      location: locationResult,
    } as Order;
  } catch (error) {
    console.error("Error getting order by ID:", error);
    return null;
  }
}
/**
 * Get order by ID (admin version - no user ID restriction)
 */
export async function getAdminOrderById(
  env: Env,
  orderId: number
): Promise<Order | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get order");
    return null;
  }

  try {
    // No user ID restriction for admin
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM orders WHERE id = ?
    `
    )
      .bind(orderId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    const order = results[0] as Order;

    // Get order items with product details including unit_type and unit_value
    const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT oi.*, p.image as product_image, p.unit_type, p.unit_value
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = ?
    `
    )
      .bind(orderId)
      .all();

    // Get order address
    const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM user_addresses WHERE id = ?
    `
    )
      .bind(order.address_id)
      .all();

    // Get order location if available
    let locationResult = null;
    if (order.location_id) {
      const { results: locationResults } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT * FROM order_locations WHERE id = ?
        `
      )
        .bind(order.location_id)
        .all();

      if (locationResults && locationResults.length > 0) {
        locationResult = locationResults[0];
      }
    }

    return {
      ...order,
      items: itemResults || [],
      address:
        addressResults && addressResults.length > 0 ? addressResults[0] : null,
      location: locationResult,
    } as Order;
  } catch (error) {
    console.error("Error getting admin order by ID:", error);
    return null;
  }
}
/**
 * Get orders for a user
 */
export async function getUserOrders(
  env: Env,
  userId: number,
  status?: string
): Promise<Order[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get orders");
    return [];
  }

  try {
    let query = `
      SELECT * FROM orders WHERE user_id = ?
    `;

    const params = [userId];

    if (status) {
      query += " AND order_status = ?";
      params.push(status);
    }

    query += " ORDER BY created_at DESC";

    const { results } = await env.SNACKSWIFT_DB.prepare(query)
      .bind(...params)
      .all();

    if (!results || results.length === 0) {
      return [];
    }

    const orders = [] as Order[];

    for (const order of results) {
      // Get order items
      const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT oi.*, p.image as product_image
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?
      `
      )
        .bind(order.id)
        .all();

      // Get order address
      const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT * FROM user_addresses WHERE id = ?
      `
      )
        .bind(order.address_id)
        .all();

      // Get order location if available
      let locationResult = null;
      if (order.location_id) {
        const { results: locationResults } = await env.SNACKSWIFT_DB.prepare(
          `
          SELECT * FROM order_locations WHERE id = ?
          `
        )
          .bind(order.location_id)
          .all();

        if (locationResults && locationResults.length > 0) {
          locationResult = locationResults[0];
        }
      }

      orders.push({
        ...order,
        items: itemResults || [],
        address:
          addressResults && addressResults.length > 0
            ? addressResults[0]
            : null,
        location: locationResult,
      } as Order);
    }

    return orders;
  } catch (error) {
    console.error("Error getting user orders:", error);
    return [];
  }
}

/**
 * Update order status
 */
export async function updateOrderStatus(
  env: Env,
  orderId: number,
  orderStatus: string
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update order status"
    );
    return false;
  }

  try {
    const now = new Date().toISOString();
    let query = `UPDATE orders SET order_status = ?, updated_at = ?`;
    const params = [orderStatus, now];

    // If order is being marked as delivered, set the delivered_at timestamp
    if (orderStatus === "delivered") {
      query += `, delivered_at = ?`;
      params.push(now);
    }

    // If order is being marked as out for delivery, set the out_for_delivery_at timestamp
    if (orderStatus === "out_for_delivery") {
      query += `, out_for_delivery_at = ?`;
      params.push(now);
    }

    // If order is being marked as cancelled, set payment_status to 'cancelled' if it's still pending
    if (orderStatus === "cancelled") {
      // Only update payment status if it's still pending
      await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE orders
        SET payment_status = 'cancelled'
        WHERE id = ? AND payment_status = 'pending'
      `
      )
        .bind(orderId)
        .run();
    }

    query += ` WHERE id = ?`;
    params.push(orderId);

    const result = await env.SNACKSWIFT_DB.prepare(query)
      .bind(...params)
      .run();
    return result && result.success;
  } catch (error) {
    console.error("Error updating order status:", error);
    return false;
  }
}

/**
 * Update order payment status
 */
export async function updateOrderPaymentStatus(
  env: Env,
  orderId: number,
  paymentStatus: string
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update payment status"
    );
    return false;
  }

  try {
    const now = new Date().toISOString();

    const result = await env.SNACKSWIFT_DB.prepare(
      `
      UPDATE orders
      SET payment_status = ?, updated_at = ?
      WHERE id = ?
    `
    )
      .bind(paymentStatus, now, orderId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error updating payment status:", error);
    return false;
  }
}

/**
 * Create a new payment transaction
 */
export async function createPaymentTransaction(
  env: Env,
  transactionData: {
    order_id: number;
    payment_id: string;
    payment_method: string;
    amount: number;
    status: string;
    gateway_response?: string;
  }
): Promise<PaymentTransaction | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't create payment transaction"
    );
    return null;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      `
      INSERT INTO payment_transactions (
        order_id, payment_id, payment_method, amount, status, gateway_response
      ) VALUES (?, ?, ?, ?, ?, ?)
    `
    )
      .bind(
        transactionData.order_id,
        transactionData.payment_id,
        transactionData.payment_method,
        transactionData.amount,
        transactionData.status,
        transactionData.gateway_response || null
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the newly created transaction
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM payment_transactions WHERE id = ?
    `
    )
      .bind(result.meta.last_row_id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as PaymentTransaction;
  } catch (error) {
    console.error("Error creating payment transaction:", error);
    return null;
  }
}

/**
 * Update payment transaction status
 */
export async function updatePaymentTransaction(
  env: Env,
  transactionData: {
    payment_id: string;
    status: string;
    gateway_response?: string;
  }
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update payment transaction"
    );
    return false;
  }

  try {
    const now = new Date().toISOString();

    const result = await env.SNACKSWIFT_DB.prepare(
      `
      UPDATE payment_transactions
      SET status = ?, gateway_response = ?, updated_at = ?
      WHERE payment_id = ?
    `
    )
      .bind(
        transactionData.status,
        transactionData.gateway_response || null,
        now,
        transactionData.payment_id
      )
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error updating payment transaction:", error);
    return false;
  }
}

/**
 * Get payment transaction by payment_id
 */
export async function getPaymentTransactionByPaymentId(
  env: Env,
  paymentId: string
): Promise<PaymentTransaction | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't get payment transaction"
    );
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM payment_transactions WHERE payment_id = ?
    `
    )
      .bind(paymentId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as PaymentTransaction;
  } catch (error) {
    console.error("Error getting payment transaction:", error);
    return null;
  }
}

/**
 * Get payment transactions for an order
 */
export async function getOrderPaymentTransactions(
  env: Env,
  orderId: number
): Promise<PaymentTransaction[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't get payment transactions"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM payment_transactions
      WHERE order_id = ?
      ORDER BY created_at DESC
    `
    )
      .bind(orderId)
      .all();

    return (results as PaymentTransaction[]) || [];
  } catch (error) {
    console.error("Error getting order payment transactions:", error);
    return [];
  }
}

/**
 * Get all order locations
 */
export async function getOrderLocations(env: Env): Promise<OrderLocation[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty locations"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM order_locations WHERE is_active = 1 ORDER BY name"
    ).all();

    return results as OrderLocation[];
  } catch (error) {
    console.error("Error fetching order locations:", error);
    return [];
  }
}

/**
 * Get all order locations (admin version - includes inactive locations)
 */
export async function getAdminOrderLocations(
  env: Env
): Promise<OrderLocation[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty locations"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM order_locations ORDER BY name"
    ).all();

    return results as OrderLocation[];
  } catch (error) {
    console.error("Error fetching admin order locations:", error);
    return [];
  }
}

/**
 * Get order location by ID
 */
export async function getOrderLocationById(
  env: Env,
  id: number
): Promise<OrderLocation | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning null location");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM order_locations WHERE id = ?"
    )
      .bind(id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as OrderLocation;
  } catch (error) {
    console.error("Error fetching order location:", error);
    return null;
  }
}

/**
 * Add new order location
 */
export async function addOrderLocation(
  env: Env,
  location: Omit<OrderLocation, "id" | "created_at">
): Promise<OrderLocation | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't add location");
    return null;
  }

  try {
    // Insert the new location
    const result = await env.SNACKSWIFT_DB.prepare(
      `INSERT INTO order_locations (name, address, is_active)
       VALUES (?, ?, ?)`
    )
      .bind(location.name, location.address, location.is_active ? 1 : 0)
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the newly created location with its ID
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM order_locations WHERE id = ?"
    )
      .bind(result.meta.last_row_id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as OrderLocation;
  } catch (error) {
    console.error("Error adding order location:", error);
    return null;
  }
}

/**
 * Update an existing order location
 */
export async function updateOrderLocation(
  env: Env,
  location: OrderLocation
): Promise<OrderLocation | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update location");
    return null;
  }

  try {
    // Update the location
    const result = await env.SNACKSWIFT_DB.prepare(
      `UPDATE order_locations
       SET name = ?, address = ?, is_active = ?
       WHERE id = ?`
    )
      .bind(
        location.name,
        location.address,
        location.is_active ? 1 : 0,
        location.id
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the updated location
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM order_locations WHERE id = ?"
    )
      .bind(location.id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as OrderLocation;
  } catch (error) {
    console.error("Error updating order location:", error);
    return null;
  }
}

/**
 * Delete an order location
 */
export async function deleteOrderLocation(
  env: Env,
  locationId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't delete location");
    return false;
  }

  try {
    // Check if location is used in any orders
    const { results: orderResults } = await env.SNACKSWIFT_DB.prepare(
      "SELECT COUNT(*) as count FROM orders WHERE location_id = ?"
    )
      .bind(locationId)
      .all();

    if (orderResults && orderResults.length > 0 && orderResults[0].count > 0) {
      // Location is used in orders, so just mark it as inactive instead of deleting
      const result = await env.SNACKSWIFT_DB.prepare(
        "UPDATE order_locations SET is_active = 0 WHERE id = ?"
      )
        .bind(locationId)
        .run();

      return result && result.success;
    }

    // Location is not used in any orders, so we can safely delete it
    const result = await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM order_locations WHERE id = ?"
    )
      .bind(locationId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error deleting order location:", error);
    return false;
  }
}

/**
 * Cancel order
 */
export async function cancelOrder(
  env: Env,
  orderId: number,
  userId: number,
  reason: string
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't cancel order");
    return false;
  }

  try {
    // Security check: ensure order belongs to user
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM orders WHERE id = ? AND user_id = ?
    `
    )
      .bind(orderId, userId)
      .all();

    if (!results || results.length === 0) {
      return false;
    }

    const order = results[0] as Order;

    // Can only cancel orders in 'placed' or 'processing' status
    if (
      order.order_status !== "placed" &&
      order.order_status !== "processing"
    ) {
      return false;
    }

    const now = new Date().toISOString();

    const result = await env.SNACKSWIFT_DB.prepare(
      `
      UPDATE orders
      SET order_status = 'cancelled', cancel_reason = ?, updated_at = ?
      WHERE id = ? AND user_id = ?
    `
    )
      .bind(reason || "Cancelled by customer", now, orderId, userId)
      .run();

    // If order had pending payment, cancel it
    if (order.payment_status === "pending") {
      await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE orders
        SET payment_status = 'cancelled', updated_at = ?
        WHERE id = ?
      `
      )
        .bind(now, orderId)
        .run();
    }

    return result && result.success;
  } catch (error) {
    console.error("Error cancelling order:", error);
    return false;
  }
}

/**
 * Get all delivery boys
 */
export async function getDeliveryBoys(env: Env): Promise<User[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty delivery boys"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE role = 'delivery_boy' ORDER BY name"
    ).all();

    return results as User[];
  } catch (error) {
    console.error("Error fetching delivery boys:", error);
    return [];
  }
}

/**
 * Get delivery boy by ID
 */
export async function getDeliveryBoyById(
  env: Env,
  userId: number
): Promise<User | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get delivery boy");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE id = ? AND role = 'delivery_boy'"
    )
      .bind(userId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as User;
  } catch (error) {
    console.error("Error getting delivery boy by ID:", error);
    return null;
  }
}

/**
 * Assign delivery boy role to a user
 */
export async function assignDeliveryBoyRole(
  env: Env,
  userId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't assign delivery boy role"
    );
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      "UPDATE users SET role = 'delivery_boy' WHERE id = ?"
    )
      .bind(userId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error assigning delivery boy role:", error);
    return false;
  }
}

/**
 * Remove delivery boy role from a user
 */
export async function removeDeliveryBoyRole(
  env: Env,
  userId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't remove delivery boy role"
    );
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      "UPDATE users SET role = 'customer' WHERE id = ? AND role = 'delivery_boy'"
    )
      .bind(userId)
      .run();

    // Also remove any location assignments
    await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM delivery_boy_locations WHERE user_id = ?"
    )
      .bind(userId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error removing delivery boy role:", error);
    return false;
  }
}

/**
 * Assign a location to a delivery boy
 */
export async function assignLocationToDeliveryBoy(
  env: Env,
  userId: number,
  locationId: number
): Promise<DeliveryBoyLocation | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't assign location");
    return null;
  }

  try {
    // Check if the user is a delivery boy
    const { results: userResults } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE id = ? AND role = 'delivery_boy'"
    )
      .bind(userId)
      .all();

    if (!userResults || userResults.length === 0) {
      console.warn("User is not a delivery boy");
      return null;
    }

    // Check if the location exists
    const { results: locationResults } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM order_locations WHERE id = ?"
    )
      .bind(locationId)
      .all();

    if (!locationResults || locationResults.length === 0) {
      console.warn("Location not found");
      return null;
    }

    // Check if the assignment already exists
    const { results: existingAssignment } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM delivery_boy_locations WHERE user_id = ? AND location_id = ?"
    )
      .bind(userId, locationId)
      .all();

    if (existingAssignment && existingAssignment.length > 0) {
      // If it exists but is inactive, reactivate it
      if (!existingAssignment[0].is_active) {
        await env.SNACKSWIFT_DB.prepare(
          "UPDATE delivery_boy_locations SET is_active = 1 WHERE id = ?"
        )
          .bind(existingAssignment[0].id)
          .run();
      }

      return {
        ...existingAssignment[0],
        user: userResults[0] as User,
        location: locationResults[0] as OrderLocation,
      } as DeliveryBoyLocation;
    }

    // Create a new assignment
    const result = await env.SNACKSWIFT_DB.prepare(
      "INSERT INTO delivery_boy_locations (user_id, location_id, is_active) VALUES (?, ?, 1)"
    )
      .bind(userId, locationId)
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Get the newly created assignment
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM delivery_boy_locations WHERE id = ?"
    )
      .bind(result.meta.last_row_id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return {
      ...results[0],
      user: userResults[0] as User,
      location: locationResults[0] as OrderLocation,
    } as DeliveryBoyLocation;
  } catch (error) {
    console.error("Error assigning location to delivery boy:", error);
    return null;
  }
}

/**
 * Remove a location assignment from a delivery boy
 */
export async function removeLocationFromDeliveryBoy(
  env: Env,
  userId: number,
  locationId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't remove location assignment"
    );
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM delivery_boy_locations WHERE user_id = ? AND location_id = ?"
    )
      .bind(userId, locationId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error removing location from delivery boy:", error);
    return false;
  }
}

/**
 * Get all locations assigned to a delivery boy
 */
export async function getDeliveryBoyLocations(
  env: Env,
  userId: number
): Promise<OrderLocation[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty locations"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT ol.*
       FROM order_locations ol
       JOIN delivery_boy_locations dbl ON ol.id = dbl.location_id
       WHERE dbl.user_id = ? AND dbl.is_active = 1 AND ol.is_active = 1
       ORDER BY ol.name`
    )
      .bind(userId)
      .all();

    return results as OrderLocation[];
  } catch (error) {
    console.error("Error fetching delivery boy locations:", error);
    return [];
  }
}

/**
 * Get all delivery boys assigned to a location
 */
export async function getLocationDeliveryBoys(
  env: Env,
  locationId: number
): Promise<User[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty delivery boys"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT u.*
       FROM users u
       JOIN delivery_boy_locations dbl ON u.id = dbl.user_id
       WHERE dbl.location_id = ? AND dbl.is_active = 1 AND u.role = 'delivery_boy'
       ORDER BY u.name`
    )
      .bind(locationId)
      .all();

    return results as User[];
  } catch (error) {
    console.error("Error fetching location delivery boys:", error);
    return [];
  }
}

/**
 * Assign a delivery boy to an order
 */
export async function assignDeliveryBoyToOrder(
  env: Env,
  orderId: number,
  deliveryBoyId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't assign delivery boy to order"
    );
    return false;
  }

  try {
    // Check if the user is a delivery boy
    const { results: userResults } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE id = ? AND role = 'delivery_boy'"
    )
      .bind(deliveryBoyId)
      .all();

    if (!userResults || userResults.length === 0) {
      console.warn("User is not a delivery boy");
      return false;
    }

    // Check if the order exists
    const { results: orderResults } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM orders WHERE id = ?"
    )
      .bind(orderId)
      .all();

    if (!orderResults || orderResults.length === 0) {
      console.warn("Order not found");
      return false;
    }

    // Update the order with the delivery boy ID
    const result = await env.SNACKSWIFT_DB.prepare(
      "UPDATE orders SET delivery_boy_id = ? WHERE id = ?"
    )
      .bind(deliveryBoyId, orderId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error assigning delivery boy to order:", error);
    return false;
  }
}

/**
 * Get orders assigned to a delivery boy
 */
export async function getDeliveryBoyOrders(
  env: Env,
  deliveryBoyId: number,
  status?: string
): Promise<Order[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty orders");
    return [];
  }

  try {
    let query = `
      SELECT o.*
      FROM orders o
      WHERE o.delivery_boy_id = ?
    `;

    const params = [deliveryBoyId];

    if (status) {
      query += " AND o.order_status = ?";
      params.push(status);
    }

    query += " ORDER BY o.created_at DESC";

    const { results } = await env.SNACKSWIFT_DB.prepare(query)
      .bind(...params)
      .all();

    if (!results || results.length === 0) {
      return [];
    }

    const orders = [] as Order[];

    for (const order of results) {
      // Get order items
      const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT oi.*, p.image as product_image
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?
      `
      )
        .bind(order.id)
        .all();

      // Get order address
      const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT * FROM user_addresses WHERE id = ?
      `
      )
        .bind(order.address_id)
        .all();

      orders.push({
        ...order,
        items: itemResults || [],
        address:
          addressResults && addressResults.length > 0
            ? addressResults[0]
            : null,
      } as Order);
    }

    return orders;
  } catch (error) {
    console.error("Error getting delivery boy orders:", error);
    return [];
  }
}

/**
 * Get orders for a location that need delivery
 */
export async function getLocationOrdersForDelivery(
  env: Env,
  locationId: number
): Promise<Order[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty orders");
    return [];
  }

  try {
    // Get orders that are ready for delivery (processing) or out for delivery
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT o.*
      FROM orders o
      WHERE o.location_id = ?
      AND (o.order_status = 'processing' OR o.order_status = 'out_for_delivery')
      ORDER BY o.created_at ASC
    `
    )
      .bind(locationId)
      .all();

    if (!results || results.length === 0) {
      return [];
    }

    const orders = [] as Order[];

    for (const order of results) {
      // Get order items
      const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT oi.*, p.image as product_image
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?
      `
      )
        .bind(order.id)
        .all();

      // Get order address
      const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT * FROM user_addresses WHERE id = ?
      `
      )
        .bind(order.address_id)
        .all();

      // Get delivery boy if assigned
      let deliveryBoy = null;
      if (order.delivery_boy_id) {
        const { results: deliveryBoyResults } = await env.SNACKSWIFT_DB.prepare(
          `
          SELECT * FROM users WHERE id = ?
        `
        )
          .bind(order.delivery_boy_id)
          .all();

        if (deliveryBoyResults && deliveryBoyResults.length > 0) {
          deliveryBoy = deliveryBoyResults[0];
        }
      }

      orders.push({
        ...order,
        items: itemResults || [],
        address:
          addressResults && addressResults.length > 0
            ? addressResults[0]
            : null,
        delivery_boy: deliveryBoy,
      } as Order);
    }

    return orders;
  } catch (error) {
    console.error("Error getting location orders for delivery:", error);
    return [];
  }
}

/**
 * Auto-assign orders to delivery boys based on location
 * This function will assign unassigned orders to available delivery boys
 * for the same location
 */
export async function autoAssignOrdersToDeliveryBoys(
  env: Env,
  locationId: number
): Promise<number> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't auto-assign orders");
    return 0;
  }

  try {
    // Get unassigned orders for the location that are in 'processing' status
    const { results: unassignedOrders } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT id
      FROM orders
      WHERE location_id = ?
      AND order_status = 'processing'
      AND delivery_boy_id IS NULL
      ORDER BY created_at ASC
    `
    )
      .bind(locationId)
      .all();

    if (!unassignedOrders || unassignedOrders.length === 0) {
      return 0; // No unassigned orders
    }

    // Get delivery boys assigned to this location
    const { results: deliveryBoys } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT u.id
      FROM users u
      JOIN delivery_boy_locations dbl ON u.id = dbl.user_id
      WHERE dbl.location_id = ?
      AND dbl.is_active = 1
      AND u.role = 'delivery_boy'
      ORDER BY u.id
    `
    )
      .bind(locationId)
      .all();

    if (!deliveryBoys || deliveryBoys.length === 0) {
      return 0; // No delivery boys for this location
    }

    // Count how many orders are assigned to each delivery boy
    const deliveryBoyOrderCounts = new Map<number, number>();

    for (const deliveryBoy of deliveryBoys) {
      const { results: orderCountResult } = await env.SNACKSWIFT_DB.prepare(
        `
        SELECT COUNT(*) as count
        FROM orders
        WHERE delivery_boy_id = ?
        AND (order_status = 'processing' OR order_status = 'out_for_delivery')
      `
      )
        .bind(deliveryBoy.id)
        .all();

      const count =
        orderCountResult && orderCountResult.length > 0
          ? orderCountResult[0].count
          : 0;

      deliveryBoyOrderCounts.set(deliveryBoy.id, count);
    }

    // Sort delivery boys by order count (ascending)
    const sortedDeliveryBoys = [...deliveryBoyOrderCounts.entries()]
      .sort((a, b) => a[1] - b[1])
      .map((entry) => entry[0]);

    // Assign orders to delivery boys in a round-robin fashion
    let assignedCount = 0;
    let deliveryBoyIndex = 0;

    for (const order of unassignedOrders) {
      if (deliveryBoyIndex >= sortedDeliveryBoys.length) {
        deliveryBoyIndex = 0; // Reset to first delivery boy if we've gone through all
      }

      const deliveryBoyId = sortedDeliveryBoys[deliveryBoyIndex];

      // Assign the order to this delivery boy
      const result = await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE orders
        SET delivery_boy_id = ?
        WHERE id = ?
      `
      )
        .bind(deliveryBoyId, order.id)
        .run();

      if (result && result.success) {
        assignedCount++;

        // Update the count for this delivery boy
        deliveryBoyOrderCounts.set(
          deliveryBoyId,
          (deliveryBoyOrderCounts.get(deliveryBoyId) || 0) + 1
        );

        // Re-sort delivery boys by order count
        const sortedDeliveryBoys = [...deliveryBoyOrderCounts.entries()]
          .sort((a, b) => a[1] - b[1])
          .map((entry) => entry[0]);

        // Find the index of the next delivery boy
        deliveryBoyIndex = 0; // Default to first
      } else {
        // Move to next delivery boy if assignment failed
        deliveryBoyIndex++;
      }
    }

    return assignedCount;
  } catch (error) {
    console.error("Error auto-assigning orders to delivery boys:", error);
    return 0;
  }
}
