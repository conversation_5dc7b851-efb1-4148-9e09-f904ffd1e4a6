---
import AdminLayout from "../../../layouts/AdminLayout.astro";
import OrderDetail from "../../../components/admin/OrderDetail.jsx";

// Get order ID from URL params
export const prerender = false;
const { id } = Astro.params;
---

<AdminLayout title="Order Details - HighQ Foods">
  <OrderDetail client:load orderId={id} />
</AdminLayout>

<script>
  // Check admin authentication on page load
//   document.addEventListener("DOMContentLoaded", () => {
//     // Check if API client is available
//     if (typeof window.ApiClient === "undefined") {
//       console.error("API client not available");
//       return;
//     }

//     // Check if user is authenticated as admin
//     if (!window.ApiClient.isAuthenticated()) {
//       // Redirect to login page
//       window.location.href = "/login?redirect=" + encodeURIComponent(window.location.pathname);
//     } else {
//     //   // Check if user has admin rights - this would be implemented in your ApiClient
//     //   window.ApiClient.checkAdminAccess()
//     //     .then(isAdmin => {
//     //       if (!isAdmin) {
//     //         // Redirect non-admin users to homepage
//     //         window.location.href = "/";
//     //       }
//     //     })
//     //     .catch(err => {
//     //       console.error("Failed to check admin status:", err);
//     //       // On error, redirect to homepage to be safe
//     //       window.location.href = "/";
//     //     });
//     }
//   });
</script>
