/**
 * Progress Bar for Page Transitions
 * Displays a loading bar at the top of the page during navigation
 */

class ProgressBar {
  constructor(options = {}) {
    this.options = {
      color: options.color || '#FF6B35',
      height: options.height || '3px',
      duration: options.duration || '0.3s',
      zIndex: options.zIndex || 9999,
      className: options.className || 'highq-progress-bar',
      minimum: options.minimum || 0.08,
      maximum: options.maximum || 0.994,
      easing: options.easing || 'cubic-bezier(0.65, 0, 0.35, 1)',
      parent: options.parent || document.body
    };

    this.progress = 0;
    this.visible = false;
    this.element = null;
    this.timeout = null;
    this.interval = null;

    this.createElements();
    this.setupEventListeners();
  }

  createElements() {
    // Create progress bar element
    this.element = document.createElement('div');
    this.element.className = this.options.className;

    // Apply styles
    Object.assign(this.element.style, {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '0%',
      height: this.options.height,
      backgroundColor: this.options.color,
      transition: `width ${this.options.duration} ${this.options.easing}`,
      zIndex: this.options.zIndex,
      opacity: '0',
      pointerEvents: 'none'
    });

    // Add to DOM
    this.options.parent.appendChild(this.element);
  }

  setupEventListeners() {
    // Handle page navigation events
    window.addEventListener('beforeunload', this.start.bind(this));
    window.addEventListener('load', () => {
      if (this.visible) {
        this.complete();
      }
    });

    window.addEventListener('error', () => {
      if (this.visible) {
        this.complete();
      }
    });

    // Handle clicks on links to start progress bar
    document.addEventListener('click', (event) => {
      // Check if the clicked element is a link or inside a link
      const link = event.target.closest('a');
      if (link &&
          link.href &&
          !link.target &&
          link.hostname === window.location.hostname &&
          !link.hasAttribute('download') &&
          !event.ctrlKey &&
          !event.metaKey) {
        this.start();

        // Simulate progress
        setTimeout(() => this.set(0.1), 10);
        setTimeout(() => {
          if (this.visible && this.progress < 0.4) {
            this.set(0.4);
          }
        }, 300);

        // For links, we need to manually handle the navigation and progress
        event.preventDefault();

        // Simulate more progress
        setTimeout(() => {
          if (this.visible && this.progress < 0.8) {
            this.set(0.8);
          }

          // Navigate to the link after showing some progress
          window.location.href = link.href;
        }, 500);
      }
    });

    // Handle form submissions
    document.addEventListener('submit', (event) => {
      const form = event.target;
      // Only show progress for forms that navigate to a new page
      if (form && form.method && form.method.toLowerCase() === 'get' && !form.hasAttribute('data-no-progress')) {
        this.start();
        setTimeout(() => this.set(0.3), 10);
      }
    });

    // Handle fetch requests (for API calls that might change page content)
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      // Only show progress for GET requests to HTML pages
      const url = args[0] instanceof Request ? args[0].url : args[0];
      const options = args[1] || {};
      const method = options.method || 'GET';

      if (method === 'GET' && url && url.toString().includes(window.location.origin) &&
          !url.toString().includes('/api/')) {
        this.start();
        setTimeout(() => this.set(0.3), 10);
      }

      try {
        const response = await originalFetch(...args);

        // If this is a navigation request, show more progress
        if (method === 'GET' && url && url.toString().includes(window.location.origin) &&
            !url.toString().includes('/api/')) {
          setTimeout(() => {
            if (this.visible) {
              this.set(0.7);
            }
          }, 200);

          setTimeout(() => {
            if (this.visible) {
              this.complete();
            }
          }, 500);
        }

        return response;
      } catch (error) {
        if (this.visible) {
          this.complete();
        }
        throw error;
      }
    };

    // Handle XMLHttpRequest for older code
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(...args) {
      const method = args[0];
      const url = args[1];

      if (method.toLowerCase() === 'get' &&
          typeof url === 'string' &&
          url.includes(window.location.origin) &&
          !url.includes('/api/')) {
        this.addEventListener('loadstart', () => {
          progressBar.start();
          setTimeout(() => progressBar.set(0.3), 10);
        });

        this.addEventListener('load', () => {
          setTimeout(() => {
            if (progressBar.visible) {
              progressBar.complete();
            }
          }, 500);
        });

        this.addEventListener('error', () => {
          if (progressBar.visible) {
            progressBar.complete();
          }
        });
      }

      return originalXHROpen.apply(this, args);
    };
  }

  start() {
    // Clear any existing timers
    clearTimeout(this.timeout);
    clearInterval(this.interval);

    // Reset progress
    this.progress = 0;

    // Show the progress bar with initial progress
    this.visible = true;
    this.element.style.opacity = '1';
    this.element.style.width = `${this.options.minimum * 100}%`;

    // Start incrementing progress slowly
    this.interval = setInterval(() => {
      if (this.progress < this.options.maximum) {
        this.increment();
      }
    }, 500);
  }

  set(progress) {
    this.progress = Math.min(progress, this.options.maximum);
    this.element.style.width = `${this.progress * 100}%`;
  }

  increment(amount = 0.05) {
    // Calculate next progress with diminishing returns as we get closer to maximum
    const nextProgress = this.progress + (amount * (1 - this.progress));
    this.set(nextProgress);
  }

  complete() {
    // Clear any existing timers
    clearTimeout(this.timeout);
    clearInterval(this.interval);

    // Set to 100% complete with a smooth animation
    this.element.style.transition = `width 0.4s cubic-bezier(0.1, 1, 0.2, 1)`;
    this.set(1);

    // Hide after a short delay to ensure the animation is visible
    this.timeout = setTimeout(() => {
      // Add a transition for opacity
      this.element.style.transition = `width 0.4s cubic-bezier(0.1, 1, 0.2, 1), opacity 0.5s ease`;
      this.visible = false;
      this.element.style.opacity = '0';

      // Reset width after fade out completes
      setTimeout(() => {
        // Reset transition to original value
        this.element.style.transition = `width ${this.options.duration} ${this.options.easing}, opacity 0.3s ease`;
        this.element.style.width = '0%';
      }, 500);
    }, 300);
  }
}

// Initialize progress bar when the script loads
let progressBar;

function initProgressBar() {
  if (!progressBar) {
    progressBar = new ProgressBar({
      color: '#FF6B35', // HighQ Foods brand color
      height: '3px'
    });
  }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initProgressBar);
