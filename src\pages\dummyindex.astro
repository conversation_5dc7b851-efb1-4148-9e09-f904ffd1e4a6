---
import MainLayout from "../layouts/MainLayout.astro";
import { getCategories, getFilteredProducts } from "../db/database";
import ProductsPage from "../components/homepage/ProductsPage";
import ProductSkeleton from "../components/common/ProductSkeleton";

// Disable prerendering to use server-side rendering with Cloudflare D1
export const prerender = false;

// Define fallback categories in case database is unavailable
const fallbackCategories = [
  { id: 1, name: "<PERSON><PERSON>", icon: "🍟", color: "#FF9F80" },
  { id: 2, name: "Cookies", icon: "🍪", color: "#E0C094" },
  { id: 3, name: "Chocolates", icon: "🍫", color: "#C79F7A" },
  { id: 4, name: "Drinks", icon: "🥤", color: "#8ECAE6" },
  { id: 5, name: "Nuts", icon: "🥜", color: "#D9BF77" },
];

// Get URL search parameters
const url = Astro.url;
const urlCategory = url.searchParams.get("category");
const urlSort = url.searchParams.get("sort") || "newest";
const urlSearch = url.searchParams.get("search") || "";
const urlMaxPrice = url.searchParams.get("maxPrice")
  ? parseInt(url.searchParams.get("maxPrice"))
  : 10000;
const urlMinPrice = url.searchParams.get("minPrice")
  ? parseInt(url.searchParams.get("minPrice"))
  : 0;
const urlTags = url.searchParams.get("tags")
  ? url.searchParams.get("tags").split(",")
  : [];

// Safely fetch categories from database with fallback
let dbCategories = [];
try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
    dbCategories = await getCategories(Astro.locals.runtime.env);
  } else {
    console.warn(
      "Runtime environment not available, using fallback categories"
    );
    dbCategories = fallbackCategories;
  }
} catch (error) {
  console.error("Error fetching categories:", error);
  dbCategories = fallbackCategories;
}

// Create a combined list with "All Products" at the beginning
const categories = [
  { id: "all", name: "All Products" },
  ...dbCategories.map((cat) => ({ id: cat.id.toString(), name: cat.name })),
];

// Fetch initial products based on URL parameters
let initialProducts = [];
let totalCount = 0;
let hasMore = false;

try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
    const page = 1;
    const limit = 12;

    // Construct filter object
    const filters = {
      category: urlCategory || "all",
      sort: urlSort,
      search: urlSearch,
      minPrice: urlMinPrice,
      maxPrice: urlMaxPrice,
      tags: urlTags,
    };

    const result = await getFilteredProducts(Astro.locals.runtime.env, {
      page,
      limit,
      ...filters,
    });

    initialProducts = result.products || [];
    totalCount = result.totalCount || 0;
    hasMore = result.hasMore || false;
  }
} catch (error) {
  console.error("Error fetching initial products:", error);
}

// Prepare initial data to pass to the React component
const initialData = {
  categories,
  products: initialProducts,
  totalCount,
  hasMore,
  activeFilters: {
    category: urlCategory || "all",
    sort: urlSort,
    search: urlSearch,
    minPrice: urlMinPrice,
    maxPrice: urlMaxPrice,
    tags: urlTags,
  },
};
---

<MainLayout
  title="Browse Products - SnackSwift"
  headerTitle="Products"
  showHeader={true}
  showBackButton={false}
>
  <ProductsPage
    client:only="react"
    initialData={initialData}
  >
    <ProductSkeleton slot="fallback" count={12} />
  </ProductsPage>
</MainLayout>

<style is:inline>
  /* Improved scrollbar hiding */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Enhanced range slider styling */
  input[type="range"] {
    -webkit-appearance: none;
    height: 6px;
    background: #e5e7eb;
    border-radius: 5px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 22px;
    width: 22px;
    border-radius: 50%;
    background: #5466f7;
    box-shadow: 0 2px 4px rgba(84, 102, 247, 0.3);
    cursor: pointer;
    border: 3px solid white;
  }

  input[type="range"]::-moz-range-thumb {
    height: 22px;
    width: 22px;
    border-radius: 50%;
    background: #5466f7;
    box-shadow: 0 2px 4px rgba(84, 102, 247, 0.3);
    cursor: pointer;
    border: 3px solid white;
  }

  input[type="range"]::-webkit-slider-runnable-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }

  /* Add smooth animations for modals */
  .transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* Improve checkbox styling */
  input[type="checkbox"],
  input[type="radio"] {
    accent-color: #5466f7;
  }

  /* Add active state styling for buttons */
  button:active:not(:disabled) {
    transform: scale(0.98);
  }

  /* Improved card hover states */
  .product-card {
    transition: all 0.2s ease-in-out;
  }

  .product-card:hover {
    transform: translateY(-2px);
    box-shadow:
      0 10px 25px -5px rgba(0, 0, 0, 0.05),
      0 8px 10px -6px rgba(0, 0, 0, 0.01);
  }

  /* Product container animations */

  @keyframes slide-from-right {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slide-to-left {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(-30px);
    }
  }

  /* Product card transition animations */
  .product-skeleton,
  .product-card {
    contain: layout;
  }
</style>
