-- Orders Management System
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS order_items;
DROP TABLE IF EXISTS payment_transactions;
-- Orders table
CREATE TABLE IF NOT EXISTS orders (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  order_number TEXT NOT NULL UNIQUE,
  total_amount REAL NOT NULL,
  delivery_fee REAL DEFAULT 0.0,
  discount_amount REAL DEFAULT 0.0,
  coupon_code TEXT,
  payment_method TEXT NOT NULL, -- 'cash', 'online', etc.
  payment_status TEXT NOT NULL, -- 'pending', 'paid', 'failed', 'refunded'
  order_status TEXT NOT NULL, -- 'placed', 'processing', 'shipped', 'delivered', 'cancelled'
  address_id INTEGER NOT NULL,
  estimated_delivery TEXT,
  delivered_at TEXT,
  cancel_reason TEXT,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIG<PERSON> KEY (address_id) REFERENCES user_addresses (id)
);

-- Create index on user_id for faster queries
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders (user_id);
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders (order_number);
CREATE INDEX IF NOT EXISTS idx_orders_order_status ON orders (order_status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders (payment_status);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  order_id INTEGER NOT NULL,
  product_id INTEGER NOT NULL,
  product_name TEXT NOT NULL,
  product_price REAL NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  total_price REAL NOT NULL,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products (id)
);

-- Create index on order_id for faster queries
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items (order_id);

-- Payment transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  order_id INTEGER NOT NULL,
  payment_id TEXT NOT NULL UNIQUE,
  payment_method TEXT NOT NULL,
  amount REAL NOT NULL,
  status TEXT NOT NULL, -- 'initiated', 'success', 'failed', 'pending'
  gateway_response TEXT,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE
);

-- Create index on order_id and payment_id for faster queries
CREATE INDEX IF NOT EXISTS idx_payment_transactions_order_id ON payment_transactions (order_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_payment_id ON payment_transactions (payment_id);

-- Trigger to update the orders updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_orders_timestamp
AFTER UPDATE ON orders
BEGIN
  UPDATE orders SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Trigger to update the payment_transactions updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_payment_transactions_timestamp
AFTER UPDATE ON payment_transactions
BEGIN
  UPDATE payment_transactions SET updated_at = datetime('now') WHERE id = NEW.id;
END;