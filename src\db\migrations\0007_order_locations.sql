-- Order Locations Table
CREATE TABLE IF NOT EXISTS order_locations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  address TEXT NOT NULL,
  is_active BOOLEAN DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_order_locations_is_active ON order_locations(is_active);

-- Add location_id field to orders table
ALTER TABLE orders ADD COLUMN location_id INTEGER;

-- Add foreign key constraint (SQLite doesn't support adding constraints in ALTER TABLE)
-- We'll handle this in application code

-- Add some sample locations
INSERT INTO order_locations (name, address, is_active) VALUES
('Main Store', 'Door No: 2-56, CHANIKYAPURI COLONY 2ND LINE, ELURU - 534002', 1),
('Branch Office', 'Near D MART, ELURU URBAN, ELURU - 534002', 1);
