---
import MainLayout from "../layouts/MainLayout.astro";
import { PhoneLogin } from "../components/auth/PhoneLogin";
---

<MainLayout
  title="Login - HighQ Foods"
  headerTitle="Login"
  showHeader={false}
  showBackButton={true}
  showFooter={false}
>
  <div class="max-w-md mx-auto px-4 py-8">
    <div class="mb-6 text-center">
      <div class="w-20 h-20 bg-orange-100 rounded-full mx-auto flex items-center justify-center mb-4">
        <span class="material-icons-round text-[#FF6B35] text-4xl">phone_android</span>
      </div>
      <h1 class="text-2xl font-bold text-gray-800 mb-2">Welcome to HighQ Foods</h1>
      <p class="text-gray-600">Login to access your account and orders</p>
      <p id="redirect-notice" class="mt-2 text-sm text-blue-600 hidden"></p>
    </div>

    <PhoneLogin client:only />

    <div class="mt-8 text-center">
      <p class="text-xs text-gray-500">
        By continuing, you agree to our Terms of Service and Privacy Policy.
      </p>
    </div>
  </div>
</MainLayout>

<script>
  // Check authentication status on page load
  document.addEventListener('DOMContentLoaded', () => {
    const redirectNotice = document.getElementById('redirect-notice');
    const urlParams = new URLSearchParams(window.location.search);
    const redirect = urlParams.get('redirect');

    // Show redirect notice if applicable
    if (redirect && redirectNotice) {
      let pageName = 'previous page';

      // Try to get a user-friendly page name
      if (redirect.includes('checkout')) {
        pageName = 'checkout';
      } else if (redirect.includes('profile')) {
        pageName = 'profile';
      } else if (redirect.includes('orders')) {
        pageName = 'orders';
      }

      redirectNotice.textContent = `You'll be redirected to ${pageName} after login`;
      redirectNotice.classList.remove('hidden');
    }

    // Check if user is already logged in
    if (typeof window.ApiClient !== 'undefined' && window.ApiClient.isAuthenticated()) {
      // Only redirect if not showing a session expired message
      if (!urlParams.get('session_expired')) {
        // Get the redirect URL or default to home
        const redirectTo = redirect || '/';

        showToast('You are already logged in');

        // Redirect to the requested page or home
        setTimeout(() => {
          window.location.href = redirectTo;
        }, 1000);
      }
    }

    // Check for session expired message
    if (urlParams.get('session_expired') === 'true') {
      showToast('Your session has expired. Please log in again.', 'error');

      // Remove the session_expired parameter but keep the redirect
      const url = new URL(window.location);
      url.searchParams.delete('session_expired');
      window.history.replaceState({}, document.title, url);
    }
  });

  // Helper function for showing toast notifications
  function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg z-50 flex items-center opacity-0 transition-opacity duration-300';

    const icon = document.createElement('span');
    icon.className = 'material-icons-round mr-2';

    if (type === 'error') {
      icon.textContent = 'error';
    } else if (type === 'success') {
      icon.textContent = 'check_circle';
    } else {
      icon.textContent = 'info';
    }

    toast.appendChild(icon);

    const messageSpan = document.createElement('span');
    messageSpan.textContent = message;
    toast.appendChild(messageSpan);

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.style.opacity = '1';
    }, 10);

    // Remove after delay
    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  }
</script>
