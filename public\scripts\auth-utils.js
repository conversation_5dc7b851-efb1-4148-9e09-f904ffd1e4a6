/**
 * Authentication utilities for SnackSwift
 */
(function() {
  window.AuthUtils = {
    /**
     * Check if the user is currently logged in
     */
    isLoggedIn: function() {
      return document.cookie.includes('session=');
    },
    
    /**
     * Get the current user from local storage cache
     * In a real app, you might want to validate the session with the server
     */
    getCurrentUser: function() {
      try {
        const userJson = localStorage.getItem('snackswift_user');
        return userJson ? JSON.parse(userJson) : null;
      } catch (error) {
        console.error('Error getting current user:', error);
        return null;
      }
    },
    
    /**
     * Set the current user in local storage
     */
    setCurrentUser: function(user) {
      if (!user) {
        localStorage.removeItem('snackswift_user');
        return;
      }
      
      localStorage.setItem('snackswift_user', JSON.stringify(user));
    },
    
    /**
     * Send OTP to the provided phone number
     */
    sendOTP: async function(phoneNumber) {
      try {
        const response = await fetch('/api/auth/send-otp', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ phoneNumber })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to send OTP');
        }
        
        return data;
        
      } catch (error) {
        console.error('Error sending OTP:', error);
        throw error;
      }
    },
    
    /**
     * Verify OTP and login the user
     */
    verifyOTP: async function(phoneNumber, otpCode) {
      try {
        const response = await fetch('/api/auth/verify-otp', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ phoneNumber, otpCode })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to verify OTP');
        }
        
        // Save user data in local storage
        if (data.user) {
          this.setCurrentUser(data.user);
        }
        
        return data;
        
      } catch (error) {
        console.error('Error verifying OTP:', error);
        throw error;
      }
    },
    
    /**
     * Logout the current user
     */
    logout: async function() {
      try {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
        
        // Clear user data from local storage
        this.setCurrentUser(null);
        
        return true;
        
      } catch (error) {
        console.error('Error during logout:', error);
        throw error;
      }
    }
  };
})();
