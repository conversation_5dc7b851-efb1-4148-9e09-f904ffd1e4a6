/**
 * Cart utilities for SnackSwift
 */
(function() {
  const cartKey = 'snackswift_cart';
  const couponKey = 'snackswift_coupon';
  const pricingKey = 'snackswift_pricing';
  const locationKey = 'snackswift_location';

  // Default pricing values
  const defaultPricing = {
    subtotal: 0,
    deliveryFee: 0,
    discount: 0,
    couponCode: '',
    total: 0
  };

  // Initialize the window.CartUtils object
  window.CartUtils = {
    /**
     * Get all cart items
     */
    getCartItems: function() {
      try {
        const cartJson = localStorage.getItem(cartKey);
        return cartJson ? JSON.parse(cartJson) : [];
      } catch (error) {
        console.error('Error getting cart items:', error);
        return [];
      }
    },

    /**
     * Add an item to cart
     */
    addToCart: function(product) {
      const cart = this.getCartItems();

      // Check if product already exists in cart
      const existingItemIndex = cart.findIndex(item => item.id == product.id);

      if (existingItemIndex !== -1) {
        // Update quantity if the product already exists
        cart[existingItemIndex].quantity += product.quantity || 1;
      } else {
        // Add the product with its unit information
        cart.push({
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.image || product.image_url,
          quantity: product.quantity || 1,
          slug: product.slug || product.url_slug,
          unit_type: product.unit_type || 'quantity',
          unit_value: product.unit_value || 1
        });
      }

      localStorage.setItem(cartKey, JSON.stringify(cart));
      this.updateCartBadge();
      this.updatePricing();

      return cart;
    },

    /**
     * Remove an item from cart
     */
    removeFromCart: function(itemId) {
      try {
        let cart = this.getCartItems();
        cart = cart.filter(item => item.id !== itemId);
        localStorage.setItem(cartKey, JSON.stringify(cart));
        this.updateCartBadge();
        this.updatePricing();
        return cart;
      } catch (error) {
        console.error('Error removing item from cart:', error);
        return [];
      }
    },

    /**
     * Update item quantity
     */
    updateQuantity: function(itemId, quantity) {
      try {
        let cart = this.getCartItems();

        if (quantity <= 0) {
          // Remove item if quantity is 0 or less
          return this.removeFromCart(itemId);
        }

        const item = cart.find(i => i.id === itemId);
        if (item) {
          item.quantity = quantity;
          localStorage.setItem(cartKey, JSON.stringify(cart));
          this.updateCartBadge();
          this.updatePricing();
        }

        return cart;
      } catch (error) {
        console.error('Error updating quantity:', error);
        return [];
      }
    },

    /**
     * Clear the cart
     */
    clearCart: function() {
      try {
        localStorage.removeItem(cartKey);
        localStorage.removeItem(couponKey);
        localStorage.removeItem(pricingKey);
        this.updateCartBadge();
        return [];
      } catch (error) {
        console.error('Error clearing cart:', error);
        return [];
      }
    },

    /**
     * Apply a coupon code
     */
    applyCoupon: async function(couponCode) {
      try {
        couponCode = couponCode.trim().toUpperCase();

        // Get current cart subtotal for validation
        const pricing = this.getPricing();

        // Validate coupon via API
        const response = await fetch('/api/coupons/validate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            code: couponCode,
            subtotal: pricing.subtotal
          })
        });

        const result = await response.json();

        if (!result.success) {
          return {
            success: false,
            message: result.message || 'Invalid coupon code'
          };
        }

        // Store the coupon code (not the full object for security)
        localStorage.setItem(couponKey, couponCode);

        // Also store the coupon details in session storage for current session use
        try {
          sessionStorage.setItem(`coupon_${couponCode}`, JSON.stringify(result.coupon));
        } catch (e) {
          console.warn('Failed to store coupon details in session storage:', e);
        }

        this.updatePricing();

        return {
          success: true,
          message: result.message || 'Coupon applied successfully!',
          coupon: result.coupon
        };
      } catch (error) {
        console.error('Error applying coupon:', error);
        return { success: false, message: 'Error applying coupon' };
      }
    },

    /**
     * Remove applied coupon
     */
    removeCoupon: function() {
      try {
        localStorage.removeItem(couponKey);
        this.updatePricing();
        return { success: true };
      } catch (error) {
        console.error('Error removing coupon:', error);
        return { success: false };
      }
    },

    /**
     * Get the currently applied coupon
     */
    getAppliedCoupon: async function() {
      try {
        const couponCode = localStorage.getItem(couponKey);
        if (!couponCode) {
          return null;
        }

        // Try to get coupon details from session storage first for better performance
        try {
          const sessionCoupon = sessionStorage.getItem(`coupon_${couponCode}`);
          if (sessionCoupon) {
            return {
              code: couponCode,
              ...JSON.parse(sessionCoupon)
            };
          }
        } catch (e) {
          console.warn('Failed to get coupon from session storage:', e);
        }

        // If not in session storage, fetch from API
        try {
          const response = await fetch('/api/coupons/validate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              code: couponCode,
              subtotal: this.getPricing().subtotal
            })
          });

          const result = await response.json();

          if (!result.success) {
            // Coupon is no longer valid - remove it
            this.removeCoupon();
            return null;
          }

          // Store for future use in this session
          try {
            sessionStorage.setItem(`coupon_${couponCode}`, JSON.stringify(result.coupon));
          } catch (e) {
            console.warn('Failed to store coupon in session storage:', e);
          }

          return {
            code: couponCode,
            ...result.coupon
          };
        } catch (error) {
          console.error('Error fetching coupon details:', error);
          return {
            code: couponCode,
            // Fallback to basic information
            type: 'unknown',
            description: 'Applied coupon'
          };
        }
      } catch (error) {
        console.error('Error getting coupon:', error);
        return null;
      }
    },

    /**
     * Set selected delivery location
     */
    setDeliveryLocation: function(locationId, locationName) {
      if (locationId) {
        localStorage.setItem(locationKey, JSON.stringify({
          id: locationId,
          name: locationName || 'Selected Location'
        }));

        // Update pricing with new location
        this.updatePricing();
      }
    },

    /**
     * Get selected delivery location
     */
    getDeliveryLocation: function() {
      try {
        const locationJson = localStorage.getItem(locationKey);
        return locationJson ? JSON.parse(locationJson) : null;
      } catch (error) {
        console.error('Error getting delivery location:', error);
        return null;
      }
    },

    /**
     * Clear selected delivery location
     */
    clearDeliveryLocation: function() {
      localStorage.removeItem(locationKey);
      this.updatePricing();
    },

    /**
     * Calculate and update pricing information
     */
    updatePricing: async function() {
      try {
        const cart = this.getCartItems();
        const couponCode = localStorage.getItem(couponKey);
        const location = this.getDeliveryLocation();

        // Calculate subtotal
        const subtotal = cart.reduce((sum, item) => sum + (parseFloat(item.price) * item.quantity), 0);

        // Start with default delivery fee
        let deliveryFee = defaultPricing.deliveryFee;
        let discount = 0;
        let freeDeliveryThreshold = 0;
        let isFreeDelivery = false;

        // Get delivery fee settings from API if we have a selected location
        if (location && location.id && typeof window.ApiClient !== 'undefined') {
          try {
            console.log('Fetching delivery fees for location:', location.id);
            const feeResponse = await window.ApiClient.getDeliveryFees(location.id);
            console.log('Delivery fee response:', feeResponse);

            if (feeResponse.success) {
              // If delivery fees are disabled globally
              if (!feeResponse.is_enabled) {
                deliveryFee = 0;
                console.log('Delivery fees are disabled globally');
              } else {
                // Use location-specific fee if available, otherwise use base fee
                if (feeResponse.location_fee) {
                  deliveryFee = parseFloat(feeResponse.location_fee.fee);
                  freeDeliveryThreshold = parseFloat(feeResponse.location_fee.free_delivery_threshold);
                  console.log('Using location-specific fee:', deliveryFee, 'threshold:', freeDeliveryThreshold);
                } else {
                  deliveryFee = parseFloat(feeResponse.base_fee);
                  freeDeliveryThreshold = parseFloat(feeResponse.free_delivery_threshold);
                  console.log('Using base fee:', deliveryFee, 'threshold:', freeDeliveryThreshold);
                }

                // Check if order qualifies for free delivery based on threshold
                if (freeDeliveryThreshold > 0 && subtotal >= freeDeliveryThreshold) {
                  deliveryFee = 0;
                  isFreeDelivery = true;
                  console.log('Order qualifies for free delivery');
                }
              }
            } else {
              console.warn('Failed to get delivery fees:', feeResponse.message);
            }
          } catch (error) {
            console.error('Error fetching delivery fees:', error);
            // Fall back to default fee
            deliveryFee = defaultPricing.deliveryFee;
          }
        } else {
          console.log('No location selected or ApiClient not available');
        }

        // Apply coupon if valid
        if (couponCode) {
          const coupon = await this.getAppliedCoupon();

          if (coupon) {
            switch (coupon.type) {
              case 'percent':
                // Calculate percentage discount
                discount = (subtotal * coupon.value / 100);

                // Apply max discount cap if specified
                if (coupon.maxDiscount && discount > coupon.maxDiscount) {
                  discount = coupon.maxDiscount;
                }
                break;
              case 'flat':
                discount = Math.min(coupon.value, subtotal); // Don't allow discount greater than subtotal
                break;
              case 'freeDelivery':
                deliveryFee = 0;
                isFreeDelivery = true;
                break;
            }
          }
        }

        // Calculate total
        const total = Math.max(0, subtotal + deliveryFee - discount);

        // Store pricing data
        const pricing = {
          subtotal,
          deliveryFee,
          discount,
          couponCode: couponCode || '',
          freeDeliveryThreshold,
          isFreeDelivery,
          total
        };

        console.log('Updated pricing:', pricing);
        localStorage.setItem(pricingKey, JSON.stringify(pricing));

        // Dispatch an event to notify components that pricing has been updated
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('pricing-updated', { detail: pricing }));
        }

        return pricing;
      } catch (error) {
        console.error('Error updating pricing:', error);
        return defaultPricing;
      }
    },

    /**
     * Get the current pricing information
     */
    getPricing: function() {
      try {
        const pricingJson = localStorage.getItem(pricingKey);
        if (!pricingJson) {
          return this.updatePricing(); // Calculate if not available
        }
        return JSON.parse(pricingJson);
      } catch (error) {
        console.error('Error getting pricing:', error);
        return defaultPricing;
      }
    },

    /**
     * Update the cart badge count in the UI
     */
    updateCartBadge: function() {
      try {
        const cart = this.getCartItems();
        const itemCount = cart.reduce((sum, item) => sum + item.quantity, 0);

        // Update all cart badges in the UI
        const badges = document.querySelectorAll('.nav-tab[data-target="cart"] .absolute, a[href="/cart"] .absolute');
        badges.forEach(badge => {
          if (itemCount > 0) {
            badge.textContent = itemCount > 9 ? '9+' : itemCount;
            badge.classList.remove('hidden');
          } else {
            badge.classList.add('hidden');
          }
        });

        return itemCount;
      } catch (error) {
        console.error('Error updating cart badge:', error);
        return 0;
      }
    }
  };

  // Initialize cart badge when the script loads
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => window.CartUtils.updateCartBadge());
  } else {
    window.CartUtils.updateCartBadge();
  }

  // Also initialize on Astro page transitions
  document.addEventListener('astro:page-load', () => {
    try {
      window.CartUtils.updateCartBadge();
    } catch (error) {
      console.error('Error updating cart badge during page transition:', error);
    }
  });

  // Notify that CartUtils is ready
  window.dispatchEvent(new CustomEvent('cart-utils-ready'));
})();
