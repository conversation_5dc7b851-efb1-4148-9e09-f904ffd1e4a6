-- User Addresses Table
CREATE TABLE IF NOT EXISTS user_addresses (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  full_name TEXT NOT NULL,
  phone TEXT NOT NULL,
  address TEXT NOT NULL,
  city TEXT NOT NULL,
  zip_code TEXT NOT NULL,
  instructions TEXT,
  is_default BOOLEAN DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Add some sample addresses
INSERT INTO user_addresses (user_id, full_name, phone, address, city, zip_code, instructions, is_default) VALUES
(1, '<PERSON>', '************', '123 Main Street', 'New York', '10001', 'Leave at the front door', 1),
(1, '<PERSON>', '************', '456 Park Avenue', 'New York', '10002', 'Ring bell twice', 0),
(2, '<PERSON>', '************', '789 Broadway', 'Los Angeles', '90001', '', 1);
