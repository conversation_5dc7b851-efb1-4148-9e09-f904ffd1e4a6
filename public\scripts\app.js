/**
 * HighQ Foods PWA Main Application Script
 */
document.addEventListener('DOMContentLoaded', () => {
  // Initialize bottom navigation functionality
  initBottomNavigation();

  // Create featured carousel timer
  initFeaturedCarouselTimer();

  // Initialize tap events for promotional banners
  initPromoBanners();
});

// Bottom navigation handler
function initBottomNavigation() {
  const navButtons = document.querySelectorAll('.nav-button');

  navButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all buttons
      navButtons.forEach(btn => btn.classList.remove('active'));

      // Add active class to clicked button
      button.classList.add('active');

      // Handle navigation action (placeholder for actual navigation)
      const target = button.getAttribute('data-target');
      console.log(`Navigate to: ${target}`);

      // Here you would implement actual page navigation or tab switching
      // For now we're just showing a notification
      showNotification(`${target.charAt(0).toUpperCase() + target.slice(1)} section selected`);
    });
  });
}

// Featured carousel auto-rotation
function initFeaturedCarouselTimer() {
  const carousel = document.getElementById('featured-carousel');
  if (!carousel) return;

  // Auto-rotate carousel every 5 seconds
  setInterval(() => {
    moveCarousel(carousel, 'next');
  }, 5000);
}

// Banner tap functionality
function initPromoBanners() {
  const promoBanners = document.querySelectorAll('.promo-banner');

  promoBanners.forEach(banner => {
    banner.addEventListener('click', () => {
      const promoId = banner.getAttribute('data-promo-id');
      const promoUrl = banner.getAttribute('data-url');

      // Here you would implement actual navigation to the promo page
      console.log(`Navigate to promo: ${promoId}, URL: ${promoUrl}`);
      showNotification(`Opening promotion: ${promoId}`);
    });
  });
}

// Utility function to show a notification toast
function showNotification(message, duration = 3000) {
  // Check if a notification container already exists
  let notificationContainer = document.querySelector('.notification-container');

  // Create container if it doesn't exist
  if (!notificationContainer) {
    notificationContainer = document.createElement('div');
    notificationContainer.className = 'notification-container';
    document.body.appendChild(notificationContainer);
  }

  // Create notification element
  const notification = document.createElement('div');
  notification.className = 'notification-toast';
  notification.textContent = message;

  // Add to container
  notificationContainer.appendChild(notification);

  // Show with animation
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);

  // Remove after duration
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300); // Wait for fade out animation
  }, duration);
}